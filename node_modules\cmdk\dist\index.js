var xe=Object.create;var K=Object.defineProperty;var Ae=Object.getOwnPropertyDescriptor;var _e=Object.getOwnPropertyNames;var we=Object.getPrototypeOf,Oe=Object.prototype.hasOwnProperty;var ke=(t,o)=>{for(var n in o)K(t,n,{get:o[n],enumerable:!0})},le=(t,o,n,a)=>{if(o&&typeof o=="object"||typeof o=="function")for(let c of _e(o))!Oe.call(t,c)&&c!==n&&K(t,c,{get:()=>o[c],enumerable:!(a=Ae(o,c))||a.enumerable});return t};var ue=(t,o,n)=>(n=t!=null?xe(we(t)):{},le(o||!t||!t.__esModule?K(n,"default",{value:t,enumerable:!0}):n,t)),Ie=t=>le(K({},"__esModule",{value:!0}),t);var Qe={};ke(Qe,{Command:()=>Xe,CommandDialog:()=>ye,CommandEmpty:()=>Pe,CommandGroup:()=>Ce,CommandInput:()=>be,CommandItem:()=>Ee,CommandList:()=>Te,CommandLoading:()=>Me,CommandRoot:()=>ee,CommandSeparator:()=>Se,useCommandState:()=>P});module.exports=Ie(Qe);var M=ue(require("@radix-ui/react-dialog")),r=ue(require("react"));var de=1,He=.9,Ne=.8,Ge=.17,X=.1,Y=.999,Ke=.9999;var Ve=.99,Fe=/[\\\/_+.#"@\[\(\{&]/,$e=/[\\\/_+.#"@\[\(\{&]/g,je=/[\s-]/,me=/[\s-]/g;function J(t,o,n,a,c,u,f){if(u===o.length)return c===t.length?de:Ve;var v=`${c},${u}`;if(f[v]!==void 0)return f[v];for(var d=a.charAt(u),p=n.indexOf(d,c),R=0,m,S,b,E;p>=0;)m=J(t,o,n,a,p+1,u+1,f),m>R&&(p===c?m*=de:Fe.test(t.charAt(p-1))?(m*=Ne,b=t.slice(c,p-1).match($e),b&&c>0&&(m*=Math.pow(Y,b.length))):je.test(t.charAt(p-1))?(m*=He,E=t.slice(c,p-1).match(me),E&&c>0&&(m*=Math.pow(Y,E.length))):(m*=Ge,c>0&&(m*=Math.pow(Y,p-c))),t.charAt(p)!==o.charAt(u)&&(m*=Ke)),(m<X&&n.charAt(p-1)===a.charAt(u+1)||a.charAt(u+1)===a.charAt(u)&&n.charAt(p-1)!==a.charAt(u))&&(S=J(t,o,n,a,p+1,u+2,f),S*X>m&&(m=S*X)),m>R&&(R=m),p=n.indexOf(d,p+1);return f[v]=R,R}function fe(t){return t.toLowerCase().replace(me," ")}function pe(t,o){return J(t,o,fe(t),fe(o),0,0,{})}var Ue='[cmdk-list-sizer=""]',k='[cmdk-group=""]',W='[cmdk-group-items=""]',qe='[cmdk-group-heading=""]',Q='[cmdk-item=""]',Re=`${Q}:not([aria-disabled="true"])`,z="cmdk-item-select",y="data-value",Be=(t,o)=>pe(t,o),ve=r.createContext(void 0),I=()=>r.useContext(ve),ge=r.createContext(void 0),Z=()=>r.useContext(ge),he=r.createContext(void 0),ee=r.forwardRef((t,o)=>{let n=r.useRef(null),a=x(()=>{var e,i,s;return{search:"",value:(s=(i=t.value)!=null?i:(e=t.defaultValue)==null?void 0:e.toLowerCase())!=null?s:"",filtered:{count:0,items:new Map,groups:new Set}}}),c=x(()=>new Set),u=x(()=>new Map),f=x(()=>new Map),v=x(()=>new Set),d=Le(t),{label:p,children:R,value:m,onValueChange:S,filter:b,shouldFilter:E,vimBindings:V=!0,..._}=t,te=r.useId(),F=r.useId(),N=r.useId(),T=We();A(()=>{if(m!==void 0){let e=m.trim().toLowerCase();a.current.value=e,T(6,ne),g.emit()}},[m]);let g=r.useMemo(()=>({subscribe:e=>(v.current.add(e),()=>v.current.delete(e)),snapshot:()=>a.current,setState:(e,i,s)=>{var l,h,C;if(!Object.is(a.current[e],i)){if(a.current[e]=i,e==="search")U(),$(),T(1,j);else if(e==="value")if(((l=d.current)==null?void 0:l.value)!==void 0){let L=i!=null?i:"";(C=(h=d.current).onValueChange)==null||C.call(h,L);return}else s||T(5,ne);g.emit()}},emit:()=>{v.current.forEach(e=>e())}}),[]),D=r.useMemo(()=>({value:(e,i)=>{i!==f.current.get(e)&&(f.current.set(e,i),a.current.filtered.items.set(e,re(i)),T(2,()=>{$(),g.emit()}))},item:(e,i)=>(c.current.add(e),i&&(u.current.has(i)?u.current.get(i).add(e):u.current.set(i,new Set([e]))),T(3,()=>{U(),$(),a.current.value||j(),g.emit()}),()=>{f.current.delete(e),c.current.delete(e),a.current.filtered.items.delete(e);let s=w();T(4,()=>{U(),(s==null?void 0:s.getAttribute("id"))===e&&j(),g.emit()})}),group:e=>(u.current.has(e)||u.current.set(e,new Set),()=>{f.current.delete(e),u.current.delete(e)}),filter:()=>d.current.shouldFilter,label:p||t["aria-label"],commandRef:n,listId:te,inputId:N,labelId:F}),[]);function re(e){var s,l;let i=(l=(s=d.current)==null?void 0:s.filter)!=null?l:Be;return e?i(e,a.current.search):0}function $(){if(!n.current||!a.current.search||d.current.shouldFilter===!1)return;let e=a.current.filtered.items,i=[];a.current.filtered.groups.forEach(l=>{let h=u.current.get(l),C=0;h.forEach(L=>{let G=e.get(L);C=Math.max(G,C)}),i.push([l,C])});let s=n.current.querySelector(Ue);O().sort((l,h)=>{var G,ie;let C=l.getAttribute(y),L=h.getAttribute(y);return((G=e.get(L))!=null?G:0)-((ie=e.get(C))!=null?ie:0)}).forEach(l=>{let h=l.closest(W);h?h.appendChild(l.parentElement===h?l:l.closest(`${W} > *`)):s.appendChild(l.parentElement===s?l:l.closest(`${W} > *`))}),i.sort((l,h)=>h[1]-l[1]).forEach(l=>{let h=n.current.querySelector(`${k}[${y}="${l[0]}"]`);h==null||h.parentElement.appendChild(h)})}function j(){let e=O().find(s=>!s.ariaDisabled),i=e==null?void 0:e.getAttribute(y);g.setState("value",i||void 0)}function U(){if(!a.current.search||d.current.shouldFilter===!1){a.current.filtered.count=c.current.size;return}a.current.filtered.groups=new Set;let e=0;for(let i of c.current){let s=f.current.get(i),l=re(s);a.current.filtered.items.set(i,l),l>0&&e++}for(let[i,s]of u.current)for(let l of s)if(a.current.filtered.items.get(l)>0){a.current.filtered.groups.add(i);break}a.current.filtered.count=e}function ne(){var i,s,l;let e=w();e&&(((i=e.parentElement)==null?void 0:i.firstChild)===e&&((l=(s=e.closest(k))==null?void 0:s.querySelector(qe))==null||l.scrollIntoView({block:"nearest"})),e.scrollIntoView({block:"nearest"}))}function w(){var e;return(e=n.current)==null?void 0:e.querySelector(`${Q}[aria-selected="true"]`)}function O(){return Array.from(n.current.querySelectorAll(Re))}function q(e){let s=O()[e];s&&g.setState("value",s.getAttribute(y))}function B(e){var C;let i=w(),s=O(),l=s.findIndex(L=>L===i),h=s[l+e];(C=d.current)!=null&&C.loop&&(h=l+e<0?s[s.length-1]:l+e===s.length?s[0]:s[l+e]),h&&g.setState("value",h.getAttribute(y))}function oe(e){let i=w(),s=i==null?void 0:i.closest(k),l;for(;s&&!l;)s=e>0?Ye(s,k):Je(s,k),l=s==null?void 0:s.querySelector(Re);l?g.setState("value",l.getAttribute(y)):B(e)}let ae=()=>q(O().length-1),ce=e=>{e.preventDefault(),e.metaKey?ae():e.altKey?oe(1):B(1)},se=e=>{e.preventDefault(),e.metaKey?q(0):e.altKey?oe(-1):B(-1)};return r.createElement("div",{ref:H([n,o]),..._,"cmdk-root":"",onKeyDown:e=>{var i;if((i=_.onKeyDown)==null||i.call(_,e),!e.defaultPrevented)switch(e.key){case"n":case"j":{V&&e.ctrlKey&&ce(e);break}case"ArrowDown":{ce(e);break}case"p":case"k":{V&&e.ctrlKey&&se(e);break}case"ArrowUp":{se(e);break}case"Home":{e.preventDefault(),q(0);break}case"End":{e.preventDefault(),ae();break}case"Enter":if(!e.nativeEvent.isComposing){e.preventDefault();let s=w();if(s){let l=new Event(z);s.dispatchEvent(l)}}}}},r.createElement("label",{"cmdk-label":"",htmlFor:D.inputId,id:D.labelId,style:ze},p),r.createElement(ge.Provider,{value:g},r.createElement(ve.Provider,{value:D},R)))}),Ee=r.forwardRef((t,o)=>{var N,T;let n=r.useId(),a=r.useRef(null),c=r.useContext(he),u=I(),f=Le(t),v=(T=(N=f.current)==null?void 0:N.forceMount)!=null?T:c==null?void 0:c.forceMount;A(()=>u.item(n,c==null?void 0:c.id),[]);let d=De(n,a,[t.value,t.children,a]),p=Z(),R=P(g=>g.value&&g.value===d.current),m=P(g=>v||u.filter()===!1?!0:g.search?g.filtered.items.get(n)>0:!0);r.useEffect(()=>{let g=a.current;if(!(!g||t.disabled))return g.addEventListener(z,S),()=>g.removeEventListener(z,S)},[m,t.onSelect,t.disabled]);function S(){var g,D;b(),(D=(g=f.current).onSelect)==null||D.call(g,d.current)}function b(){p.setState("value",d.current,!0)}if(!m)return null;let{disabled:E,value:V,onSelect:_,forceMount:te,...F}=t;return r.createElement("div",{ref:H([a,o]),...F,id:n,"cmdk-item":"",role:"option","aria-disabled":E||void 0,"aria-selected":R||void 0,"data-disabled":E||void 0,"data-selected":R||void 0,onPointerMove:E?void 0:b,onClick:E?void 0:S},t.children)}),Ce=r.forwardRef((t,o)=>{let{heading:n,children:a,forceMount:c,...u}=t,f=r.useId(),v=r.useRef(null),d=r.useRef(null),p=r.useId(),R=I(),m=P(E=>c||R.filter()===!1?!0:E.search?E.filtered.groups.has(f):!0);A(()=>R.group(f),[]),De(f,v,[t.value,t.heading,d]);let S=r.useMemo(()=>({id:f,forceMount:c}),[c]),b=r.createElement(he.Provider,{value:S},a);return r.createElement("div",{ref:H([v,o]),...u,"cmdk-group":"",role:"presentation",hidden:m?void 0:!0},n&&r.createElement("div",{ref:d,"cmdk-group-heading":"","aria-hidden":!0,id:p},n),r.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":n?p:void 0},b))}),Se=r.forwardRef((t,o)=>{let{alwaysRender:n,...a}=t,c=r.useRef(null),u=P(f=>!f.search);return!n&&!u?null:r.createElement("div",{ref:H([c,o]),...a,"cmdk-separator":"",role:"separator"})}),be=r.forwardRef((t,o)=>{let{onValueChange:n,...a}=t,c=t.value!=null,u=Z(),f=P(R=>R.search),v=P(R=>R.value),d=I(),p=r.useMemo(()=>{var m;let R=(m=d.commandRef.current)==null?void 0:m.querySelector(`${Q}[${y}="${v}"]`);return R==null?void 0:R.getAttribute("id")},[v,d.commandRef]);return r.useEffect(()=>{t.value!=null&&u.setState("search",t.value)},[t.value]),r.createElement("input",{ref:o,...a,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":d.listId,"aria-labelledby":d.labelId,"aria-activedescendant":p,id:d.inputId,type:"text",value:c?t.value:f,onChange:R=>{c||u.setState("search",R.target.value),n==null||n(R.target.value)}})}),Te=r.forwardRef((t,o)=>{let{children:n,...a}=t,c=r.useRef(null),u=r.useRef(null),f=I();return r.useEffect(()=>{if(u.current&&c.current){let v=u.current,d=c.current,p,R=new ResizeObserver(()=>{p=requestAnimationFrame(()=>{let m=v.offsetHeight;d.style.setProperty("--cmdk-list-height",m.toFixed(1)+"px")})});return R.observe(v),()=>{cancelAnimationFrame(p),R.unobserve(v)}}},[]),r.createElement("div",{ref:H([c,o]),...a,"cmdk-list":"",role:"listbox","aria-label":"Suggestions",id:f.listId,"aria-labelledby":f.inputId},r.createElement("div",{ref:u,"cmdk-list-sizer":""},n))}),ye=r.forwardRef((t,o)=>{let{open:n,onOpenChange:a,overlayClassName:c,contentClassName:u,container:f,...v}=t;return r.createElement(M.Root,{open:n,onOpenChange:a},r.createElement(M.Portal,{container:f},r.createElement(M.Overlay,{"cmdk-overlay":"",className:c}),r.createElement(M.Content,{"aria-label":t.label,"cmdk-dialog":"",className:u},r.createElement(ee,{ref:o,...v}))))}),Pe=r.forwardRef((t,o)=>{let n=r.useRef(!0),a=P(c=>c.filtered.count===0);return r.useEffect(()=>{n.current=!1},[]),n.current||!a?null:r.createElement("div",{ref:o,...t,"cmdk-empty":"",role:"presentation"})}),Me=r.forwardRef((t,o)=>{let{progress:n,children:a,...c}=t;return r.createElement("div",{ref:o,...c,"cmdk-loading":"",role:"progressbar","aria-valuenow":n,"aria-valuemin":0,"aria-valuemax":100,"aria-label":"Loading..."},r.createElement("div",{"aria-hidden":!0},a))}),Xe=Object.assign(ee,{List:Te,Item:Ee,Input:be,Group:Ce,Separator:Se,Dialog:ye,Empty:Pe,Loading:Me});function Ye(t,o){let n=t.nextElementSibling;for(;n;){if(n.matches(o))return n;n=n.nextElementSibling}}function Je(t,o){let n=t.previousElementSibling;for(;n;){if(n.matches(o))return n;n=n.previousElementSibling}}function Le(t){let o=r.useRef(t);return A(()=>{o.current=t}),o}var A=typeof window=="undefined"?r.useEffect:r.useLayoutEffect;function x(t){let o=r.useRef();return o.current===void 0&&(o.current=t()),o}function H(t){return o=>{t.forEach(n=>{typeof n=="function"?n(o):n!=null&&(n.current=o)})}}function P(t){let o=Z(),n=()=>t(o.snapshot());return r.useSyncExternalStore(o.subscribe,n,n)}function De(t,o,n){let a=r.useRef(),c=I();return A(()=>{var f;let u=(()=>{var v;for(let d of n){if(typeof d=="string")return d.trim().toLowerCase();if(typeof d=="object"&&"current"in d)return d.current?(v=d.current.textContent)==null?void 0:v.trim().toLowerCase():a.current}})();c.value(t,u),(f=o.current)==null||f.setAttribute(y,u),a.current=u}),a}var We=()=>{let[t,o]=r.useState(),n=x(()=>new Map);return A(()=>{n.current.forEach(a=>a()),n.current=new Map},[t]),(a,c)=>{n.current.set(a,c),o({})}},ze={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};0&&(module.exports={Command,CommandDialog,CommandEmpty,CommandGroup,CommandInput,CommandItem,CommandList,CommandLoading,CommandRoot,CommandSeparator,useCommandState});
