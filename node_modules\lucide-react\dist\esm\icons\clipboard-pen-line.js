/**
 * @license lucide-react v0.312.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const ClipboardPenLine = createLucideIcon("ClipboardPenLine", [
  ["rect", { width: "8", height: "4", x: "8", y: "2", rx: "1", key: "1oijnt" }],
  ["path", { d: "M8 4H6a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-.5", key: "1but9f" }],
  ["path", { d: "M16 4h2a2 2 0 0 1 1.73 1", key: "1p8n7l" }],
  ["path", { d: "M8 18h1", key: "13wk12" }],
  ["path", { d: "M18.4 9.6a2 2 0 0 1 3 3L17 17l-4 1 1-4Z", key: "yg2pdb" }]
]);

export { ClipboardPenLine as default };
//# sourceMappingURL=clipboard-pen-line.js.map
