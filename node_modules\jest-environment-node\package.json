{"name": "jest-environment-node", "version": "30.0.5", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-environment-node"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/environment": "30.0.5", "@jest/fake-timers": "30.0.5", "@jest/types": "30.0.5", "@types/node": "*", "jest-mock": "30.0.5", "jest-util": "30.0.5", "jest-validate": "30.0.5"}, "devDependencies": {"@jest/test-utils": "30.0.5", "clsx": "^2.1.1"}, "scripts": {"test:base": "echo GLOBALS_CLEANUP=$GLOBALS_CLEANUP && yarn --cwd='../.' jest --runInBand packages/jest-environment-node/src/__tests__", "test:globals-cleanup-off": "GLOBALS_CLEANUP=off yarn test:base", "test:globals-cleanup-soft": "GLOBALS_CLEANUP=soft yarn test:base", "test:globals-cleanup-on": "GLOBALS_CLEANUP=on yarn test:base", "test": "yarn test:globals-cleanup-off && yarn test:globals-cleanup-soft && yarn test:globals-cleanup-on"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "22236cf58b66039f81893537c90dee290bab427f"}