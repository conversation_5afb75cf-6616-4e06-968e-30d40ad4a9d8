{"mappings": ";AAOA,0BAAoB,SAAQ,KAAK,CAAC,cAAc,CAAC,WAAW,CAAC;IAC3D,QAAQ,CAAC,EAAE,MAAM,SAAS,CAAC;CAC5B;AAED,OAAA,MAAM,mFAoCJ,CAAC;AA+BH,OAAA,MAAM;cAAuC,MAAM,SAAS;iBAE3D,CAAC;AAqCF,OAAA,MAAM,mFAAW,CAAC", "sources": ["packages/react/slot/src/packages/react/slot/src/Slot.tsx", "packages/react/slot/src/packages/react/slot/src/index.ts", "packages/react/slot/src/index.ts"], "sourcesContent": [null, null, "export {\n  Slot,\n  Slottable,\n  //\n  Root,\n} from './Slot';\nexport type { SlotProps } from './Slot';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}