{"mappings": ";;;ACAA;;AAWA,SAASA,wCAAT,CAAiC,EAXjC,MAYEG,IAD+B,CAAA,EAXjC,aAaEC,WAF+B,CAAA,YAG/BC,QAAQ,GAAG,IAAM,EAAjBA,GAHF,EAIkC;IAChC,MAAM,CAACC,gBAAD,EAAmBC,mBAAnB,CAAA,GAA0CC,0CAAoB,CAAC;QAhBvE,aAgByEJ,WAAF;QAhBvE,UAgBsFC,QAAAA;KAAhB,CAApE,AAAqE;IACrE,MAAMI,YAAY,GAAGN,IAAI,KAAKO,SAA9B,AAAA;IACA,MAAMC,MAAK,GAAGF,YAAY,GAAGN,IAAH,GAAUG,gBAApC,AAAA;IACA,MAAMM,YAAY,GAAGV,qBAAc,CAACG,QAAD,CAAnC,AAAA;IAEA,MAAMQ,QAA6D,GAAGZ,kBAAA,CACnEc,CAAAA,SAAD,GAAe;QACb,IAAIN,YAAJ,EAAkB;YAChB,MAAMO,MAAM,GAAGD,SAAf,AAAA;YACA,MAAMJ,KAAK,GAAG,OAAOI,SAAP,KAAqB,UAArB,GAAkCC,MAAM,CAACb,IAAD,CAAxC,GAAiDY,SAA/D,AAAA;YACA,IAAIJ,KAAK,KAAKR,IAAd,EAAoBS,YAAY,CAACD,KAAD,CAAZ,CAApB;SAHF,MAKEJ,mBAAmB,CAACQ,SAAD,CAAnB,CAAAR;KAPgE,EAUpE;QAACE,YAAD;QAAeN,IAAf;QAAqBI,mBAArB;QAA0CK,YAA1C;KAVoE,CAAtE,AASG;IAIH,OAAO;QAACD,MAAD;QAAQE,QAAR;KAAP,CAAA;CACD;AAED,SAASL,0CAAT,CAAiC,EArCjC,aAsCEJ,WAD+B,CAAA,EArCjC,UAuCEC,QAAAA,CAAAA,EAFF,EAGgD;IAC9C,MAAMY,iBAAiB,GAAGhB,eAAA,CAA8BG,WAA9B,CAA1B,AAAA;IACA,MAAM,CAACO,KAAD,CAAA,GAAUM,iBAAhB,AAAA;IACA,MAAME,YAAY,GAAGlB,aAAA,CAAaU,KAAb,CAArB,AAAA;IACA,MAAMC,YAAY,GAAGV,qBAAc,CAACG,QAAD,CAAnC,AAAA;IAEAJ,gBAAA,CAAgB,IAAM;QACpB,IAAIkB,YAAY,CAACG,OAAb,KAAyBX,KAA7B,EAAoC;YAClCC,YAAY,CAACD,KAAD,CAAZ,CAAAC;YACAO,YAAY,CAACG,OAAb,GAAuBX,KAAvB,CAAAQ;SACD;KAJH,EAKG;QAACR,KAAD;QAAQQ,YAAR;QAAsBP,YAAtB;KALH,CAKC,CAAA;IAED,OAAOK,iBAAP,CAAA;CACD;;ADtDD", "sources": ["packages/react/use-controllable-state/src/index.ts", "packages/react/use-controllable-state/src/useControllableState.tsx"], "sourcesContent": ["export { useControllableState } from './useControllableState';\n", "import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\ntype UseControllableStateParams<T> = {\n  prop?: T | undefined;\n  defaultProp?: T | undefined;\n  onChange?: (state: T) => void;\n};\n\ntype SetStateFn<T> = (prevState?: T) => T;\n\nfunction useControllableState<T>({\n  prop,\n  defaultProp,\n  onChange = () => {},\n}: UseControllableStateParams<T>) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== undefined;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = useCallbackRef(onChange);\n\n  const setValue: React.Dispatch<React.SetStateAction<T | undefined>> = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue as SetStateFn<T>;\n        const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n        if (value !== prop) handleChange(value as T);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n\n  return [value, setValue] as const;\n}\n\nfunction useUncontrolledState<T>({\n  defaultProp,\n  onChange,\n}: Omit<UseControllableStateParams<T>, 'prop'>) {\n  const uncontrolledState = React.useState<T | undefined>(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = React.useRef(value);\n  const handleChange = useCallbackRef(onChange);\n\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value as T);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n\n  return uncontrolledState;\n}\n\nexport { useControllableState };\n"], "names": ["useControllableState", "React", "useCallbackRef", "prop", "defaultProp", "onChange", "uncontrolledProp", "setUncontrolledProp", "useUncontrolledState", "isControlled", "undefined", "value", "handleChange", "setValue", "useCallback", "nextValue", "setter", "uncontrolledState", "useState", "prevValueRef", "useRef", "useEffect", "current"], "version": 3, "file": "index.module.js.map"}