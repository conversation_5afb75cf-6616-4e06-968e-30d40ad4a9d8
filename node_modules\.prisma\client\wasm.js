
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.AssessmentScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  assessorName: 'assessorName',
  assessmentDate: 'assessmentDate',
  status: 'status'
};

exports.Prisma.DemographicsScalarFieldEnum = {
  id: 'id',
  assessmentId: 'assessmentId',
  patientCode: 'patientCode',
  dateOfBirth: 'dateOfBirth',
  age: 'age',
  gender: 'gender',
  generalLocation: 'generalLocation',
  region: 'region',
  ethnicity: 'ethnicity',
  race: 'race',
  primaryLanguage: 'primaryLanguage',
  maritalStatus: 'maritalStatus',
  education: 'education',
  occupation: 'occupation',
  employmentStatus: 'employmentStatus',
  livingArrangement: 'livingArrangement',
  insuranceType: 'insuranceType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SymptomAssessmentScalarFieldEnum = {
  id: 'id',
  assessmentId: 'assessmentId',
  symptomId: 'symptomId',
  severity: 'severity',
  duration: 'duration',
  frequency: 'frequency',
  notes: 'notes',
  createdAt: 'createdAt'
};

exports.Prisma.SymptomScalarFieldEnum = {
  id: 'id',
  name: 'name',
  category: 'category',
  description: 'description'
};

exports.Prisma.RiskAssessmentScalarFieldEnum = {
  id: 'id',
  assessmentId: 'assessmentId',
  suicidalIdeation: 'suicidalIdeation',
  suicidalPlan: 'suicidalPlan',
  suicidalMeans: 'suicidalMeans',
  suicidalAttemptHistory: 'suicidalAttemptHistory',
  suicidalRiskLevel: 'suicidalRiskLevel',
  homicidalIdeation: 'homicidalIdeation',
  violenceHistory: 'violenceHistory',
  violenceRiskLevel: 'violenceRiskLevel',
  selfHarmHistory: 'selfHarmHistory',
  selfHarmRisk: 'selfHarmRisk',
  substanceUseRisk: 'substanceUseRisk',
  riskFactors: 'riskFactors',
  protectiveFactors: 'protectiveFactors',
  interventions: 'interventions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MedicalHistoryScalarFieldEnum = {
  id: 'id',
  assessmentId: 'assessmentId',
  currentMedications: 'currentMedications',
  allergies: 'allergies',
  medicalConditions: 'medicalConditions',
  otherMedicalConditions: 'otherMedicalConditions',
  surgicalHistory: 'surgicalHistory',
  previousPsychiatricTreatment: 'previousPsychiatricTreatment',
  previousHospitalizations: 'previousHospitalizations',
  previousMedications: 'previousMedications',
  familyPsychiatricHistory: 'familyPsychiatricHistory',
  alcoholUse: 'alcoholUse',
  drugUse: 'drugUse',
  tobaccoUse: 'tobaccoUse',
  substanceAbuseHistory: 'substanceAbuseHistory',
  structuredMedicalConditions: 'structuredMedicalConditions',
  substanceUseHistory: 'substanceUseHistory',
  traumaHistory: 'traumaHistory',
  traumaDetails: 'traumaDetails',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MentalStatusExamScalarFieldEnum = {
  id: 'id',
  assessmentId: 'assessmentId',
  appearance: 'appearance',
  behavior: 'behavior',
  attitude: 'attitude',
  speechRate: 'speechRate',
  speechVolume: 'speechVolume',
  speechTone: 'speechTone',
  mood: 'mood',
  affect: 'affect',
  affectRange: 'affectRange',
  affectIntensity: 'affectIntensity',
  thoughtProcess: 'thoughtProcess',
  thoughtContent: 'thoughtContent',
  hallucinations: 'hallucinations',
  hallucinationType: 'hallucinationType',
  delusions: 'delusions',
  delusionType: 'delusionType',
  orientation: 'orientation',
  attention: 'attention',
  concentration: 'concentration',
  memory: 'memory',
  insight: 'insight',
  judgment: 'judgment',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DiagnosisAssessmentScalarFieldEnum = {
  id: 'id',
  assessmentId: 'assessmentId',
  diagnosisId: 'diagnosisId',
  type: 'type',
  confidence: 'confidence',
  notes: 'notes',
  createdAt: 'createdAt'
};

exports.Prisma.DiagnosisScalarFieldEnum = {
  id: 'id',
  code: 'code',
  name: 'name',
  category: 'category',
  description: 'description'
};

exports.Prisma.MedicalConditionScalarFieldEnum = {
  id: 'id',
  assessmentId: 'assessmentId',
  conditionName: 'conditionName',
  isPresent: 'isPresent',
  notes: 'notes',
  createdAt: 'createdAt'
};

exports.Prisma.PsychiatricEpisodeScalarFieldEnum = {
  id: 'id',
  assessmentId: 'assessmentId',
  episodeType: 'episodeType',
  duration: 'duration',
  durationUnit: 'durationUnit',
  startDate: 'startDate',
  endDate: 'endDate',
  severity: 'severity',
  treatmentReceived: 'treatmentReceived',
  treatmentResponse: 'treatmentResponse',
  notes: 'notes',
  createdAt: 'createdAt'
};

exports.Prisma.MedicationHistoryScalarFieldEnum = {
  id: 'id',
  assessmentId: 'assessmentId',
  medicationName: 'medicationName',
  category: 'category',
  dosage: 'dosage',
  startDate: 'startDate',
  endDate: 'endDate',
  effectiveness: 'effectiveness',
  sideEffects: 'sideEffects',
  discontinuationReason: 'discontinuationReason',
  discontinuationOther: 'discontinuationOther',
  notes: 'notes',
  createdAt: 'createdAt'
};

exports.Prisma.LaboratoryTestScalarFieldEnum = {
  id: 'id',
  assessmentId: 'assessmentId',
  testName: 'testName',
  category: 'category',
  datePerformed: 'datePerformed',
  result: 'result',
  normalRange: 'normalRange',
  notes: 'notes',
  createdAt: 'createdAt'
};

exports.Prisma.PsychologicalAssessmentScalarFieldEnum = {
  id: 'id',
  assessmentId: 'assessmentId',
  testName: 'testName',
  datePerformed: 'datePerformed',
  score: 'score',
  interpretation: 'interpretation',
  notes: 'notes',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  Assessment: 'Assessment',
  Demographics: 'Demographics',
  SymptomAssessment: 'SymptomAssessment',
  Symptom: 'Symptom',
  RiskAssessment: 'RiskAssessment',
  MedicalHistory: 'MedicalHistory',
  MentalStatusExam: 'MentalStatusExam',
  DiagnosisAssessment: 'DiagnosisAssessment',
  Diagnosis: 'Diagnosis',
  MedicalCondition: 'MedicalCondition',
  PsychiatricEpisode: 'PsychiatricEpisode',
  MedicationHistory: 'MedicationHistory',
  LaboratoryTest: 'LaboratoryTest',
  PsychologicalAssessment: 'PsychologicalAssessment'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
