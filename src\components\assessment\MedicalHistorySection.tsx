"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Plus, Trash2 } from "lucide-react"
import SubstanceUseSection, { SubstanceUseEntry } from "./SubstanceUseSection"
import MedicationHistorySection, { MedicationEntry } from "./MedicationHistorySection"


// Common psychiatric-relevant medical conditions
const MEDICAL_CONDITIONS = [
  'Diabetes Mellitus',
  'Hypertension',
  'Epilepsy/Seizure Disorder',
  'Head trauma with loss of consciousness',
  'Hypothyroidism',
  'Hyperthyroidism',
  'Cardiovascular disease',
  'Chronic kidney disease',
  'Liver disease',
  'Autoimmune disorders'
]

interface MedicalConditions {
  [key: string]: boolean
}

// Psychiatric episode types and related data
const EPISODE_TYPES = [
  'Major Depressive Episode',
  'Manic Episode',
  'Hypomanic Episode',
  'Mixed Episode',
  'Psychotic Episode',
  'Anxiety Episode',
  'Other'
]

const DURATION_UNITS = ['days', 'weeks', 'months', 'years']
const SEVERITY_LEVELS = ['Mild', 'Moderate', 'Severe']
const TREATMENT_TYPES = ['Medication', 'Therapy', 'Hospitalization', 'None']
const TREATMENT_RESPONSES = ['Good', 'Partial', 'Poor', 'Unknown']

interface PsychiatricEpisode {
  id: string
  episodeType: string
  duration: string
  durationUnit: string
  startDate: string // month/year format
  endDate: string // month/year format, optional for ongoing
  severity: string
  treatmentReceived: string[]
  treatmentResponse: string
  notes: string
}

interface MedicalHistoryData {
  currentMedications?: string
  allergies?: string
  medicalConditions?: string // Legacy field for backward compatibility
  structuredMedicalConditions?: MedicalConditions
  otherMedicalConditions?: string
  surgicalHistory?: string
  previousPsychiatricTreatment?: boolean
  previousHospitalizations?: string
  previousMedications?: string
  psychiatricEpisodes?: PsychiatricEpisode[]
  medicationHistory?: MedicationEntry[]

  familyPsychiatricHistory?: string
  substanceUseHistory?: SubstanceUseEntry[]
}

interface MedicalHistorySectionProps {
  data: MedicalHistoryData
  onUpdate: (data: MedicalHistoryData) => void
}

export default function MedicalHistorySection({ data, onUpdate }: MedicalHistorySectionProps) {
  const [formData, setFormData] = useState<MedicalHistoryData>(data || {})
  const [selectedEpisodeType, setSelectedEpisodeType] = useState<string>('')

  useEffect(() => {
    onUpdate(formData)
  }, [formData, onUpdate])

  const handleBooleanChange = (field: keyof MedicalHistoryData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value === 'true'
    }))
  }

  const handleStringChange = (field: keyof MedicalHistoryData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubstanceUseUpdate = useCallback((substanceUseHistory: SubstanceUseEntry[]) => {
    setFormData(prev => ({
      ...prev,
      substanceUseHistory
    }))
  }, [])

  const handleMedicationHistoryUpdate = useCallback((medicationHistory: MedicationEntry[]) => {
    setFormData(prev => ({
      ...prev,
      medicationHistory
    }))
  }, [])



  const handleMedicalConditionChange = (condition: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      structuredMedicalConditions: {
        ...prev.structuredMedicalConditions,
        [condition]: checked
      }
    }))
  }

  const addPsychiatricEpisode = () => {
    if (!selectedEpisodeType) return

    const newEpisode: PsychiatricEpisode = {
      id: Date.now().toString(),
      episodeType: selectedEpisodeType,
      duration: '',
      durationUnit: '',
      startDate: '',
      endDate: '',
      severity: '',
      treatmentReceived: [],
      treatmentResponse: '',
      notes: ''
    }

    setFormData(prev => ({
      ...prev,
      psychiatricEpisodes: [...(prev.psychiatricEpisodes || []), newEpisode]
    }))
    setSelectedEpisodeType('')
  }

  const removePsychiatricEpisode = (id: string) => {
    setFormData(prev => ({
      ...prev,
      psychiatricEpisodes: prev.psychiatricEpisodes?.filter(e => e.id !== id) || []
    }))
  }

  const updatePsychiatricEpisode = (id: string, field: keyof PsychiatricEpisode, value: string | string[]) => {
    setFormData(prev => ({
      ...prev,
      psychiatricEpisodes: prev.psychiatricEpisodes?.map(e =>
        e.id === id ? { ...e, [field]: value } : e
      ) || []
    }))
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Medical History</h2>
        <p className="text-sm text-slate-600">Provide comprehensive medical and psychiatric history information.</p>
      </div>

      {/* Medical History */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Current Medical Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Allergies</Label>
            <Textarea
              value={formData.allergies || ''}
              onChange={(e) => handleStringChange('allergies', e.target.value)}
              placeholder="List any known allergies to medications, foods, or other substances"
              rows={2}
            />
          </div>

          <div className="space-y-2">
            <Label>Surgical History</Label>
            <Textarea
              value={formData.surgicalHistory || ''}
              onChange={(e) => handleStringChange('surgicalHistory', e.target.value)}
              placeholder="List any past surgeries and dates"
              rows={2}
            />
          </div>
        </CardContent>
      </Card>

      {/* Medical Conditions (moved to its own section) */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Medical Conditions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {MEDICAL_CONDITIONS.map((condition) => (
              <div key={condition} className="flex items-center space-x-2">
                <Checkbox
                  id={condition}
                  checked={formData.structuredMedicalConditions?.[condition] || false}
                  onCheckedChange={(checked) => handleMedicalConditionChange(condition, checked as boolean)}
                />
                <Label htmlFor={condition} className="text-sm font-normal cursor-pointer">
                  {condition}
                </Label>
              </div>
            ))}
          </div>

          <div className="space-y-2">
            <Label>Other Medical Conditions</Label>
            <Textarea
              value={formData.otherMedicalConditions || ''}
              onChange={(e) => handleStringChange('otherMedicalConditions', e.target.value)}
              placeholder="List any additional medical conditions not covered above"
              rows={2}
            />
          </div>
        </CardContent>
      </Card>

      {/* Current Medications (moved to its own section) */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Current Medications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Textarea
              value={formData.currentMedications || ''}
              onChange={(e) => handleStringChange('currentMedications', e.target.value)}
              placeholder="List all current medications, dosages, and frequency"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Psychiatric History Episodes */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Psychiatric History Episodes</CardTitle>
          <CardDescription>
            Track specific psychiatric episodes with detailed information for comprehensive assessment
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Add New Episode */}
          <div className="border rounded-lg p-4 bg-slate-50">
            <h4 className="font-medium mb-3">Add Psychiatric Episode</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Episode Type</Label>
                <Select value={selectedEpisodeType} onValueChange={setSelectedEpisodeType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select episode type" />
                  </SelectTrigger>
                  <SelectContent>
                    {EPISODE_TYPES.map(type => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end">
                <Button
                  onClick={addPsychiatricEpisode}
                  disabled={!selectedEpisodeType}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Episode
                </Button>
              </div>
            </div>
          </div>

          {/* Current Episodes List */}
          {formData.psychiatricEpisodes && formData.psychiatricEpisodes.length > 0 && (
            <div className="space-y-4">
              <h4 className="font-medium">Psychiatric Episodes</h4>
              {formData.psychiatricEpisodes.map((episode) => (
                <Card key={episode.id} className="border-l-4 border-l-purple-500">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{episode.episodeType}</Badge>
                        {episode.severity && <Badge variant="secondary">{episode.severity}</Badge>}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removePsychiatricEpisode(episode.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label>Duration</Label>
                        <div className="flex space-x-2">
                          <Input
                            value={episode.duration}
                            onChange={(e) => updatePsychiatricEpisode(episode.id, 'duration', e.target.value)}
                            placeholder="e.g., 2"
                            type="number"
                            className="flex-1"
                          />
                          <Select
                            value={episode.durationUnit}
                            onValueChange={(value) => updatePsychiatricEpisode(episode.id, 'durationUnit', value)}
                          >
                            <SelectTrigger className="w-24">
                              <SelectValue placeholder="Unit" />
                            </SelectTrigger>
                            <SelectContent>
                              {DURATION_UNITS.map(unit => (
                                <SelectItem key={unit} value={unit}>
                                  {unit}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>Start Date (Month/Year)</Label>
                        <Input
                          value={episode.startDate}
                          onChange={(e) => updatePsychiatricEpisode(episode.id, 'startDate', e.target.value)}
                          placeholder="e.g., 03/2023"
                          type="month"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>End Date (Month/Year)</Label>
                        <Input
                          value={episode.endDate}
                          onChange={(e) => updatePsychiatricEpisode(episode.id, 'endDate', e.target.value)}
                          placeholder="Leave empty if ongoing"
                          type="month"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Severity</Label>
                        <Select
                          value={episode.severity}
                          onValueChange={(value) => updatePsychiatricEpisode(episode.id, 'severity', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select severity" />
                          </SelectTrigger>
                          <SelectContent>
                            {SEVERITY_LEVELS.map(level => (
                              <SelectItem key={level} value={level}>
                                {level}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Treatment Response</Label>
                        <Select
                          value={episode.treatmentResponse}
                          onValueChange={(value) => updatePsychiatricEpisode(episode.id, 'treatmentResponse', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select response" />
                          </SelectTrigger>
                          <SelectContent>
                            {TREATMENT_RESPONSES.map(response => (
                              <SelectItem key={response} value={response}>
                                {response}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Treatment Received</Label>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {TREATMENT_TYPES.map((treatment) => (
                          <div key={treatment} className="flex items-center space-x-2">
                            <Checkbox
                              id={`${episode.id}-${treatment}`}
                              checked={episode.treatmentReceived.includes(treatment)}
                              onCheckedChange={(checked) => {
                                const currentTreatments = episode.treatmentReceived
                                const newTreatments = checked
                                  ? [...currentTreatments, treatment]
                                  : currentTreatments.filter(t => t !== treatment)
                                updatePsychiatricEpisode(episode.id, 'treatmentReceived', newTreatments)
                              }}
                            />
                            <Label htmlFor={`${episode.id}-${treatment}`} className="text-sm">
                              {treatment}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Additional Notes</Label>
                      <Textarea
                        value={episode.notes}
                        onChange={(e) => updatePsychiatricEpisode(episode.id, 'notes', e.target.value)}
                        placeholder="Additional details about the episode, triggers, symptoms, etc."
                        rows={2}
                      />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {(!formData.psychiatricEpisodes || formData.psychiatricEpisodes.length === 0) && (
            <div className="text-center py-8 text-slate-500">
              <p>No psychiatric episodes recorded.</p>
              <p className="text-sm">Use the form above to add episode information.</p>
            </div>
          )}

          {/* Family Psychiatric History */}
          <div className="space-y-2 pt-4 border-t">
            <Label>Family Psychiatric History</Label>
            <Textarea
              value={formData.familyPsychiatricHistory || ''}
              onChange={(e) => handleStringChange('familyPsychiatricHistory', e.target.value)}
              placeholder="Describe any family history of mental illness, suicide, or substance abuse"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Substance Use History */}
      <SubstanceUseSection
        data={formData.substanceUseHistory || []}
        onUpdate={handleSubstanceUseUpdate}
      />

      {/* Medication History */}
      <MedicationHistorySection
        data={formData.medicationHistory || []}
        onUpdate={handleMedicationHistoryUpdate}
      />




    </div>
  )
}
