"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Plus, X, Trash2 } from "lucide-react"

// DSM-5 Substance Categories
const DSM5_CATEGORIES = {
  'Alcohol': [
    'Beer', 'Wine', 'Spirits/Liquor', 'Mixed Drinks', 'Other Alcohol'
  ],
  'Cannabis': [
    'Marijuana/THC', 'Hashish', 'CBD Products', 'Synthetic Cannabis (K2/Spice)', 'Other Cannabis'
  ],
  'Hallucinogens': [
    'LSD', 'PCP', 'Psilocybin (Mushrooms)', 'MDMA/Ecstasy', 'Mescaline', 'DMT', 'Other Hallucinogens'
  ],
  'Inhalants': [
    'Nitrous Oxide', 'Glue/Solvents', 'Gasoline', 'Paint Thinner', 'Aerosols', 'Other Inhalants'
  ],
  'Opioids': [
    'Heroin', 'Fentanyl', 'Oxycodone', 'Hydrocodone', 'Morphine', 'Codeine', 'Methadone', 'Buprenorphine', 'Other Opioids'
  ],
  'Sedatives/Hypnotics/Anxiolytics': [
    'Benzodiazepines (Xanax, Valium, etc.)', 'Barbiturates', 'Sleep Medications (Ambien, etc.)', 'Other Sedatives'
  ],
  'Stimulants': [
    'Cocaine', 'Crack Cocaine', 'Amphetamines', 'Methamphetamine', 'ADHD Medications (Adderall, etc.)', 'Other Stimulants'
  ],
  'Tobacco': [
    'Cigarettes', 'Cigars', 'Pipe Tobacco', 'Chewing Tobacco', 'E-cigarettes/Vaping', 'Other Tobacco'
  ],
  'Caffeine': [
    'Coffee', 'Tea', 'Energy Drinks', 'Caffeine Pills', 'Other Caffeine'
  ],
  'Other/Unknown': [
    'Prescription Drugs (Misused)', 'Over-the-Counter Drugs (Misused)', 'Unknown Substance', 'Other'
  ]
}

const ROUTES_OF_ADMINISTRATION = [
  'Oral', 'Intravenous (IV)', 'Intranasal (Snorting)', 'Smoking/Inhalation', 
  'Sublingual', 'Transdermal', 'Intramuscular', 'Subcutaneous', 'Other'
]

const FREQUENCY_PATTERNS = [
  'Daily', 'Multiple times per day', 'Weekly', 'Multiple times per week', 
  'Monthly', 'Occasionally', 'Binges/Episodes', 'As needed', 'Other'
]

interface SubstanceUseEntry {
  id: string
  category: string
  specificSubstance: string
  duration: string
  route: string
  frequency: string
  ageOfFirstUse: string
  treatmentHistory: string
  notes: string
}

interface SubstanceUseSectionProps {
  data: SubstanceUseEntry[]
  onUpdate: (data: SubstanceUseEntry[]) => void
}

export default function SubstanceUseSection({ data, onUpdate }: SubstanceUseSectionProps) {
  const [substances, setSubstances] = useState<SubstanceUseEntry[]>(data || [])
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [selectedSubstance, setSelectedSubstance] = useState<string>('')

  // Initialize from props data
  useEffect(() => {
    if (data && data.length !== substances.length) {
      setSubstances(data)
    }
  }, [data])

  // Call onUpdate when substances change
  useEffect(() => {
    onUpdate(substances)
  }, [substances])

  const addSubstance = () => {
    if (!selectedCategory || !selectedSubstance) return

    const newSubstance: SubstanceUseEntry = {
      id: Date.now().toString(),
      category: selectedCategory,
      specificSubstance: selectedSubstance,
      duration: '',
      route: '',
      frequency: '',
      ageOfFirstUse: '',
      treatmentHistory: '',
      notes: ''
    }

    setSubstances(prev => [...prev, newSubstance])
    setSelectedCategory('')
    setSelectedSubstance('')
  }

  const removeSubstance = (id: string) => {
    setSubstances(prev => prev.filter(s => s.id !== id))
  }

  const updateSubstance = (id: string, field: keyof SubstanceUseEntry, value: string) => {
    setSubstances(prev => prev.map(s => 
      s.id === id ? { ...s, [field]: value } : s
    ))
  }

  const availableSubstances = selectedCategory ? DSM5_CATEGORIES[selectedCategory as keyof typeof DSM5_CATEGORIES] || [] : []

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Enhanced Substance Use History</CardTitle>
        <CardDescription>
          Detailed substance use assessment using DSM-5 categories for comprehensive evaluation
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Add New Substance */}
        <div className="border rounded-lg p-4 bg-slate-50">
          <h4 className="font-medium mb-3">Add Substance Use History</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>DSM-5 Category</Label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {Object.keys(DSM5_CATEGORIES).map(category => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Specific Substance</Label>
              <Select 
                value={selectedSubstance} 
                onValueChange={setSelectedSubstance}
                disabled={!selectedCategory}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select substance" />
                </SelectTrigger>
                <SelectContent>
                  {availableSubstances.map(substance => (
                    <SelectItem key={substance} value={substance}>
                      {substance}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button 
                onClick={addSubstance}
                disabled={!selectedCategory || !selectedSubstance}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Substance
              </Button>
            </div>
          </div>
        </div>

        {/* Current Substances List */}
        {substances.length > 0 && (
          <div className="space-y-4">
            <h4 className="font-medium">Current Substance Use History</h4>
            {substances.map((substance) => (
              <Card key={substance.id} className="border-l-4 border-l-blue-500">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{substance.category}</Badge>
                      <span className="font-medium">{substance.specificSubstance}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSubstance(substance.id)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Duration of Use</Label>
                      <Input
                        value={substance.duration}
                        onChange={(e) => updateSubstance(substance.id, 'duration', e.target.value)}
                        placeholder="e.g., 2 years, 6 months"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Route of Administration</Label>
                      <Select 
                        value={substance.route} 
                        onValueChange={(value) => updateSubstance(substance.id, 'route', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select route" />
                        </SelectTrigger>
                        <SelectContent>
                          {ROUTES_OF_ADMINISTRATION.map(route => (
                            <SelectItem key={route} value={route}>
                              {route}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Frequency/Pattern</Label>
                      <Select 
                        value={substance.frequency} 
                        onValueChange={(value) => updateSubstance(substance.id, 'frequency', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select frequency" />
                        </SelectTrigger>
                        <SelectContent>
                          {FREQUENCY_PATTERNS.map(freq => (
                            <SelectItem key={freq} value={freq}>
                              {freq}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Age of First Use</Label>
                      <Input
                        value={substance.ageOfFirstUse}
                        onChange={(e) => updateSubstance(substance.id, 'ageOfFirstUse', e.target.value)}
                        placeholder="e.g., 16"
                        type="number"
                      />
                    </div>

                    <div className="space-y-2 md:col-span-2">
                      <Label>Treatment History</Label>
                      <Input
                        value={substance.treatmentHistory}
                        onChange={(e) => updateSubstance(substance.id, 'treatmentHistory', e.target.value)}
                        placeholder="Previous treatment attempts, rehab, etc."
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Additional Notes</Label>
                    <Textarea
                      value={substance.notes}
                      onChange={(e) => updateSubstance(substance.id, 'notes', e.target.value)}
                      placeholder="Additional details about use patterns, triggers, consequences, etc."
                      rows={2}
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {substances.length === 0 && (
          <div className="text-center py-8 text-slate-500">
            <p>No substance use history recorded.</p>
            <p className="text-sm">Use the form above to add substance use information.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export type { SubstanceUseEntry }
