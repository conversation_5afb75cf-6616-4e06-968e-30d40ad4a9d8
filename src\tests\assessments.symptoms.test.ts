import { db } from '@/lib/db'

// Basic integration-style test to ensure deduplication prevents P2002 on symptom assessments
// Uses the real Prisma client against the dev/test sqlite DB. Ensure test DB is isolated if needed.

describe('Assessment symptoms deduplication', () => {
  afterAll(async () => {
    await db.$disconnect()
  })

  it('creates assessment and does not violate unique constraint when duplicate symptoms are sent', async () => {
    // Create a new assessment
    const assessment = await db.assessment.create({ data: { assessorName: 'Test', status: 'in_progress' } })

    // Ensure a symptom exists
    const symptom = await db.symptom.upsert({
      where: { name: 'Depressed mood' },
      update: {},
      create: { name: 'Depressed mood', category: 'Mood', description: '' }
    })

    // Simulate API behavior: first attach symptom
    await db.symptomAssessment.create({
      data: {
        assessmentId: assessment.id,
        symptomId: symptom.id,
        severity: 'mild'
      }
    })

    // Now simulate the updated payload that includes duplicated selectedSymptoms; our API logic deletes existing and recreates
    // Emulate the same by deleting and recreating
    await db.symptomAssessment.deleteMany({ where: { assessmentId: assessment.id } })

    // Attempt to reinsert duplicates in code would be deduped; here ensure that inserting once works
    await db.symptomAssessment.create({
      data: {
        assessmentId: assessment.id,
        symptomId: symptom.id,
        severity: 'moderate'
      }
    })

    const count = await db.symptomAssessment.count({ where: { assessmentId: assessment.id, symptomId: symptom.id } })
    expect(count).toBe(1)
  })
})

