import{a as oe}from"./chunk-XJATAMEX.mjs";import*as y from"@radix-ui/react-dialog";import*as t from"react";var fe='[cmdk-list-sizer=""]',O='[cmdk-group=""]',U='[cmdk-group-items=""]',me='[cmdk-group-heading=""]',W='[cmdk-item=""]',ae=`${W}:not([aria-disabled="true"])`,B="cmdk-item-select",S="data-value",pe=(r,c)=>oe(r,c),se=t.createContext(void 0),A=()=>t.useContext(se),ie=t.createContext(void 0),J=()=>t.useContext(ie),ce=t.createContext(void 0),le=t.forwardRef((r,c)=>{let n=t.useRef(null),o=w(()=>{var e,s,a;return{search:"",value:(a=(s=r.value)!=null?s:(e=r.defaultValue)==null?void 0:e.toLowerCase())!=null?a:"",filtered:{count:0,items:new Map,groups:new Set}}}),l=w(()=>new Set),d=w(()=>new Map),f=w(()=>new Map),g=w(()=>new Set),u=ue(r),{label:b,children:p,value:R,onValueChange:T,filter:I,shouldFilter:C,vimBindings:F=!0,...P}=r,Q=t.useId(),K=t.useId(),V=t.useId(),E=Le();D(()=>{if(R!==void 0){let e=R.trim().toLowerCase();o.current.value=e,E(6,Y),m.emit()}},[R]);let m=t.useMemo(()=>({subscribe:e=>(g.current.add(e),()=>g.current.delete(e)),snapshot:()=>o.current,setState:(e,s,a)=>{var i,v,h;if(!Object.is(o.current[e],s)){if(o.current[e]=s,e==="search")$(),N(),E(1,j);else if(e==="value")if(((i=u.current)==null?void 0:i.value)!==void 0){let x=s!=null?s:"";(h=(v=u.current).onValueChange)==null||h.call(v,x);return}else a||E(5,Y);m.emit()}},emit:()=>{g.current.forEach(e=>e())}}),[]),M=t.useMemo(()=>({value:(e,s)=>{s!==f.current.get(e)&&(f.current.set(e,s),o.current.filtered.items.set(e,X(s)),E(2,()=>{N(),m.emit()}))},item:(e,s)=>(l.current.add(e),s&&(d.current.has(s)?d.current.get(s).add(e):d.current.set(s,new Set([e]))),E(3,()=>{$(),N(),o.current.value||j(),m.emit()}),()=>{f.current.delete(e),l.current.delete(e),o.current.filtered.items.delete(e);let a=k();E(4,()=>{$(),(a==null?void 0:a.getAttribute("id"))===e&&j(),m.emit()})}),group:e=>(d.current.has(e)||d.current.set(e,new Set),()=>{f.current.delete(e),d.current.delete(e)}),filter:()=>u.current.shouldFilter,label:b||r["aria-label"],commandRef:n,listId:Q,inputId:V,labelId:K}),[]);function X(e){var a,i;let s=(i=(a=u.current)==null?void 0:a.filter)!=null?i:pe;return e?s(e,o.current.search):0}function N(){if(!n.current||!o.current.search||u.current.shouldFilter===!1)return;let e=o.current.filtered.items,s=[];o.current.filtered.groups.forEach(i=>{let v=d.current.get(i),h=0;v.forEach(x=>{let G=e.get(x);h=Math.max(G,h)}),s.push([i,h])});let a=n.current.querySelector(fe);H().sort((i,v)=>{var G,re;let h=i.getAttribute(S),x=v.getAttribute(S);return((G=e.get(x))!=null?G:0)-((re=e.get(h))!=null?re:0)}).forEach(i=>{let v=i.closest(U);v?v.appendChild(i.parentElement===v?i:i.closest(`${U} > *`)):a.appendChild(i.parentElement===a?i:i.closest(`${U} > *`))}),s.sort((i,v)=>v[1]-i[1]).forEach(i=>{let v=n.current.querySelector(`${O}[${S}="${i[0]}"]`);v==null||v.parentElement.appendChild(v)})}function j(){let e=H().find(a=>!a.ariaDisabled),s=e==null?void 0:e.getAttribute(S);m.setState("value",s||void 0)}function $(){if(!o.current.search||u.current.shouldFilter===!1){o.current.filtered.count=l.current.size;return}o.current.filtered.groups=new Set;let e=0;for(let s of l.current){let a=f.current.get(s),i=X(a);o.current.filtered.items.set(s,i),i>0&&e++}for(let[s,a]of d.current)for(let i of a)if(o.current.filtered.items.get(i)>0){o.current.filtered.groups.add(s);break}o.current.filtered.count=e}function Y(){var s,a,i;let e=k();e&&(((s=e.parentElement)==null?void 0:s.firstChild)===e&&((i=(a=e.closest(O))==null?void 0:a.querySelector(me))==null||i.scrollIntoView({block:"nearest"})),e.scrollIntoView({block:"nearest"}))}function k(){var e;return(e=n.current)==null?void 0:e.querySelector(`${W}[aria-selected="true"]`)}function H(){return Array.from(n.current.querySelectorAll(ae))}function q(e){let a=H()[e];a&&m.setState("value",a.getAttribute(S))}function z(e){var h;let s=k(),a=H(),i=a.findIndex(x=>x===s),v=a[i+e];(h=u.current)!=null&&h.loop&&(v=i+e<0?a[a.length-1]:i+e===a.length?a[0]:a[i+e]),v&&m.setState("value",v.getAttribute(S))}function Z(e){let s=k(),a=s==null?void 0:s.closest(O),i;for(;a&&!i;)a=e>0?ye(a,O):xe(a,O),i=a==null?void 0:a.querySelector(ae);i?m.setState("value",i.getAttribute(S)):z(e)}let ee=()=>q(H().length-1),te=e=>{e.preventDefault(),e.metaKey?ee():e.altKey?Z(1):z(1)},ne=e=>{e.preventDefault(),e.metaKey?q(0):e.altKey?Z(-1):z(-1)};return t.createElement("div",{ref:_([n,c]),...P,"cmdk-root":"",onKeyDown:e=>{var s;if((s=P.onKeyDown)==null||s.call(P,e),!e.defaultPrevented)switch(e.key){case"n":case"j":{F&&e.ctrlKey&&te(e);break}case"ArrowDown":{te(e);break}case"p":case"k":{F&&e.ctrlKey&&ne(e);break}case"ArrowUp":{ne(e);break}case"Home":{e.preventDefault(),q(0);break}case"End":{e.preventDefault(),ee();break}case"Enter":if(!e.nativeEvent.isComposing){e.preventDefault();let a=k();if(a){let i=new Event(B);a.dispatchEvent(i)}}}}},t.createElement("label",{"cmdk-label":"",htmlFor:M.inputId,id:M.labelId,style:Te},b),t.createElement(ie.Provider,{value:m},t.createElement(se.Provider,{value:M},p)))}),ve=t.forwardRef((r,c)=>{var V,E;let n=t.useId(),o=t.useRef(null),l=t.useContext(ce),d=A(),f=ue(r),g=(E=(V=f.current)==null?void 0:V.forceMount)!=null?E:l==null?void 0:l.forceMount;D(()=>d.item(n,l==null?void 0:l.id),[]);let u=de(n,o,[r.value,r.children,o]),b=J(),p=L(m=>m.value&&m.value===u.current),R=L(m=>g||d.filter()===!1?!0:m.search?m.filtered.items.get(n)>0:!0);t.useEffect(()=>{let m=o.current;if(!(!m||r.disabled))return m.addEventListener(B,T),()=>m.removeEventListener(B,T)},[R,r.onSelect,r.disabled]);function T(){var m,M;I(),(M=(m=f.current).onSelect)==null||M.call(m,u.current)}function I(){b.setState("value",u.current,!0)}if(!R)return null;let{disabled:C,value:F,onSelect:P,forceMount:Q,...K}=r;return t.createElement("div",{ref:_([o,c]),...K,id:n,"cmdk-item":"",role:"option","aria-disabled":C||void 0,"aria-selected":p||void 0,"data-disabled":C||void 0,"data-selected":p||void 0,onPointerMove:C?void 0:I,onClick:C?void 0:T},r.children)}),ge=t.forwardRef((r,c)=>{let{heading:n,children:o,forceMount:l,...d}=r,f=t.useId(),g=t.useRef(null),u=t.useRef(null),b=t.useId(),p=A(),R=L(C=>l||p.filter()===!1?!0:C.search?C.filtered.groups.has(f):!0);D(()=>p.group(f),[]),de(f,g,[r.value,r.heading,u]);let T=t.useMemo(()=>({id:f,forceMount:l}),[l]),I=t.createElement(ce.Provider,{value:T},o);return t.createElement("div",{ref:_([g,c]),...d,"cmdk-group":"",role:"presentation",hidden:R?void 0:!0},n&&t.createElement("div",{ref:u,"cmdk-group-heading":"","aria-hidden":!0,id:b},n),t.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":n?b:void 0},I))}),Re=t.forwardRef((r,c)=>{let{alwaysRender:n,...o}=r,l=t.useRef(null),d=L(f=>!f.search);return!n&&!d?null:t.createElement("div",{ref:_([l,c]),...o,"cmdk-separator":"",role:"separator"})}),be=t.forwardRef((r,c)=>{let{onValueChange:n,...o}=r,l=r.value!=null,d=J(),f=L(p=>p.search),g=L(p=>p.value),u=A(),b=t.useMemo(()=>{var R;let p=(R=u.commandRef.current)==null?void 0:R.querySelector(`${W}[${S}="${g}"]`);return p==null?void 0:p.getAttribute("id")},[g,u.commandRef]);return t.useEffect(()=>{r.value!=null&&d.setState("search",r.value)},[r.value]),t.createElement("input",{ref:c,...o,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":u.listId,"aria-labelledby":u.labelId,"aria-activedescendant":b,id:u.inputId,type:"text",value:l?r.value:f,onChange:p=>{l||d.setState("search",p.target.value),n==null||n(p.target.value)}})}),he=t.forwardRef((r,c)=>{let{children:n,...o}=r,l=t.useRef(null),d=t.useRef(null),f=A();return t.useEffect(()=>{if(d.current&&l.current){let g=d.current,u=l.current,b,p=new ResizeObserver(()=>{b=requestAnimationFrame(()=>{let R=g.offsetHeight;u.style.setProperty("--cmdk-list-height",R.toFixed(1)+"px")})});return p.observe(g),()=>{cancelAnimationFrame(b),p.unobserve(g)}}},[]),t.createElement("div",{ref:_([l,c]),...o,"cmdk-list":"",role:"listbox","aria-label":"Suggestions",id:f.listId,"aria-labelledby":f.inputId},t.createElement("div",{ref:d,"cmdk-list-sizer":""},n))}),Ee=t.forwardRef((r,c)=>{let{open:n,onOpenChange:o,overlayClassName:l,contentClassName:d,container:f,...g}=r;return t.createElement(y.Root,{open:n,onOpenChange:o},t.createElement(y.Portal,{container:f},t.createElement(y.Overlay,{"cmdk-overlay":"",className:l}),t.createElement(y.Content,{"aria-label":r.label,"cmdk-dialog":"",className:d},t.createElement(le,{ref:c,...g}))))}),Se=t.forwardRef((r,c)=>{let n=t.useRef(!0),o=L(l=>l.filtered.count===0);return t.useEffect(()=>{n.current=!1},[]),n.current||!o?null:t.createElement("div",{ref:c,...r,"cmdk-empty":"",role:"presentation"})}),Ce=t.forwardRef((r,c)=>{let{progress:n,children:o,...l}=r;return t.createElement("div",{ref:c,...l,"cmdk-loading":"",role:"progressbar","aria-valuenow":n,"aria-valuemin":0,"aria-valuemax":100,"aria-label":"Loading..."},t.createElement("div",{"aria-hidden":!0},o))}),we=Object.assign(le,{List:he,Item:ve,Input:be,Group:ge,Separator:Re,Dialog:Ee,Empty:Se,Loading:Ce});function ye(r,c){let n=r.nextElementSibling;for(;n;){if(n.matches(c))return n;n=n.nextElementSibling}}function xe(r,c){let n=r.previousElementSibling;for(;n;){if(n.matches(c))return n;n=n.previousElementSibling}}function ue(r){let c=t.useRef(r);return D(()=>{c.current=r}),c}var D=typeof window=="undefined"?t.useEffect:t.useLayoutEffect;function w(r){let c=t.useRef();return c.current===void 0&&(c.current=r()),c}function _(r){return c=>{r.forEach(n=>{typeof n=="function"?n(c):n!=null&&(n.current=c)})}}function L(r){let c=J(),n=()=>r(c.snapshot());return t.useSyncExternalStore(c.subscribe,n,n)}function de(r,c,n){let o=t.useRef(),l=A();return D(()=>{var f;let d=(()=>{var g;for(let u of n){if(typeof u=="string")return u.trim().toLowerCase();if(typeof u=="object"&&"current"in u)return u.current?(g=u.current.textContent)==null?void 0:g.trim().toLowerCase():o.current}})();l.value(r,d),(f=c.current)==null||f.setAttribute(S,d),o.current=d}),o}var Le=()=>{let[r,c]=t.useState(),n=w(()=>new Map);return D(()=>{n.current.forEach(o=>o()),n.current=new Map},[r]),(o,l)=>{n.current.set(o,l),c({})}},Te={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};export{we as Command,Ee as CommandDialog,Se as CommandEmpty,ge as CommandGroup,be as CommandInput,ve as CommandItem,he as CommandList,Ce as CommandLoading,le as CommandRoot,Re as CommandSeparator,L as useCommandState};
