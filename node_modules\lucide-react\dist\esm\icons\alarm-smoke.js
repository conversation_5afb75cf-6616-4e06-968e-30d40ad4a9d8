/**
 * @license lucide-react v0.312.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const AlarmSmoke = createLucideIcon("AlarmSmoke", [
  ["path", { d: "M4 8a2 2 0 0 1-2-2V3h20v3a2 2 0 0 1-2 2Z", key: "2c4fvq" }],
  ["path", { d: "m19 8-.8 3c-.1.6-.6 1-1.2 1H7c-.6 0-1.1-.4-1.2-1L5 8", key: "1vrndv" }],
  ["path", { d: "M16 21c0-2.5 2-2.5 2-5", key: "1o3eny" }],
  ["path", { d: "M11 21c0-2.5 2-2.5 2-5", key: "1sicvv" }],
  ["path", { d: "M6 21c0-2.5 2-2.5 2-5", key: "i3w1gp" }]
]);

export { AlarmSmoke as default };
//# sourceMappingURL=alarm-smoke.js.map
