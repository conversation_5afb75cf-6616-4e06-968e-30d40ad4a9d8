/**
 * @license lucide-react v0.312.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const MessageCircleCode = createLucideIcon("MessageCircleCode", [
  ["path", { d: "M7.9 20A9 9 0 1 0 4 16.1L2 22Z", key: "vv11sd" }],
  ["path", { d: "m10 10-2 2 2 2", key: "p6et6i" }],
  ["path", { d: "m14 10 2 2-2 2", key: "1kkmpt" }]
]);

export { MessageCircleCode as default };
//# sourceMappingURL=message-circle-code.js.map
