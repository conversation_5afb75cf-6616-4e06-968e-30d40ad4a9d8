{"name": "@radix-ui/primitive", "version": "1.0.0", "license": "MIT", "source": "src/index.ts", "main": "dist/index.js", "module": "dist/index.module.js", "types": "dist/index.d.ts", "files": ["dist", "README.md"], "sideEffects": false, "scripts": {"clean": "rm -rf dist", "version": "yarn version"}, "homepage": "https://radix-ui.com/primitives", "repository": {"type": "git", "url": "git+https://github.com/radix-ui/primitives.git"}, "bugs": {"url": "https://github.com/radix-ui/primitives/issues"}, "dependencies": {"@babel/runtime": "^7.13.10"}}