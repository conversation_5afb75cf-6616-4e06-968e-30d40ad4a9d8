import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Check if this is an update to an existing assessment
    let assessment
    if (body.assessmentId) {
      // Update existing assessment
      assessment = await db.assessment.update({
        where: { id: body.assessmentId },
        data: {
          assessorName: body.assessorName || 'Anonymous',
          status: body.status || 'in_progress',
          updatedAt: new Date()
        }
      })
    } else {
      // Create a new assessment
      assessment = await db.assessment.create({
        data: {
          assessorName: body.assessorName || 'Anonymous',
          status: 'in_progress'
        }
      })
    }

    // Create or update demographics if provided
    if (body.demographics && Object.keys(body.demographics).length > 0) {
      // Sanitize and normalize demographics fields (e.g., dateOfBirth)
      const demographicsData: any = { ...body.demographics }

      if (typeof demographicsData.dateOfBirth !== 'undefined') {
        const dob = demographicsData.dateOfBirth
        if (dob === null || (typeof dob === 'string' && dob.trim() === '')) {
          // Remove empty string/null to satisfy Prisma optional DateTime
          delete demographicsData.dateOfBirth
        } else if (typeof dob === 'string') {
          // Convert date-only string to Date object (Prisma accepts JS Date)
          if (/^\d{4}-\d{2}-\d{2}$/.test(dob)) {
            demographicsData.dateOfBirth = new Date(dob)
          } else {
            const parsed = new Date(dob)
            if (!isNaN(parsed.getTime())) {
              demographicsData.dateOfBirth = parsed
            } else {
              // If invalid, remove to avoid Prisma validation error
              delete demographicsData.dateOfBirth
            }
          }
        }
      }

      // Use upsert to create or update demographics
      await db.demographics.upsert({
        where: { assessmentId: assessment.id },
        update: demographicsData,
        create: {
          assessmentId: assessment.id,
          ...demographicsData
        }
      })
    }

    // Create risk assessment if provided
    if (body.riskAssessment && Object.keys(body.riskAssessment).length > 0) {
      await db.riskAssessment.create({
        data: {
          assessmentId: assessment.id,
          ...body.riskAssessment
        }
      })
    }

    // Create medical history if provided
    if (body.medicalHistory && Object.keys(body.medicalHistory).length > 0) {
      const medicalHistoryData = { ...body.medicalHistory }

      // Handle structured data by converting to JSON strings
      if (medicalHistoryData.structuredMedicalConditions) {
        medicalHistoryData.structuredMedicalConditions = JSON.stringify(medicalHistoryData.structuredMedicalConditions)
      }
      if (medicalHistoryData.substanceUseHistory) {
        medicalHistoryData.substanceUseHistory = JSON.stringify(medicalHistoryData.substanceUseHistory)
      }

      // Remove complex objects that should be handled separately
      const { psychiatricEpisodes, medicationHistory, testsData, ...basicMedicalHistory } = medicalHistoryData

      await db.medicalHistory.upsert({
        where: { assessmentId: assessment.id },
        update: basicMedicalHistory,
        create: {
          assessmentId: assessment.id,
          ...basicMedicalHistory
        }
      })

      // Handle psychiatric episodes
      if (body.medicalHistory.psychiatricEpisodes && Array.isArray(body.medicalHistory.psychiatricEpisodes)) {
        // Delete existing episodes for this assessment
        await db.psychiatricEpisode.deleteMany({
          where: { assessmentId: assessment.id }
        })

        // Create new episodes
        for (const episode of body.medicalHistory.psychiatricEpisodes) {
          await db.psychiatricEpisode.create({
            data: {
              assessmentId: assessment.id,
              episodeType: episode.episodeType,
              duration: episode.duration,
              durationUnit: episode.durationUnit,
              startDate: episode.startDate,
              endDate: episode.endDate,
              severity: episode.severity,
              treatmentReceived: JSON.stringify(episode.treatmentReceived || []),
              treatmentResponse: episode.treatmentResponse,
              notes: episode.notes
            }
          })
        }
      }

      // Handle medication history
      if (body.medicalHistory.medicationHistory && Array.isArray(body.medicalHistory.medicationHistory)) {
        // Delete existing medication history for this assessment
        await db.medicationHistory.deleteMany({
          where: { assessmentId: assessment.id }
        })

        // Create new medication entries
        for (const medication of body.medicalHistory.medicationHistory) {
          await db.medicationHistory.create({
            data: {
              assessmentId: assessment.id,
              medicationName: medication.medicationName,
              category: medication.category,
              dosage: medication.dosage,
              startDate: medication.startDate,
              endDate: medication.endDate,
              effectiveness: medication.effectiveness,
              sideEffects: medication.sideEffects,
              discontinuationReason: medication.discontinuationReason,
              discontinuationOther: medication.discontinuationOther,
              notes: medication.notes
            }
          })
        }
      }

      // Handle tests data
      if (body.medicalHistory.testsData) {
        const testsData = body.medicalHistory.testsData

        // Delete existing test results for this assessment
        await db.laboratoryTest.deleteMany({
          where: { assessmentId: assessment.id }
        })
        await db.psychologicalAssessment.deleteMany({
          where: { assessmentId: assessment.id }
        })

        // Create new test results
        if (testsData.testResults && Array.isArray(testsData.testResults)) {
          for (const testResult of testsData.testResults) {
            if (testResult.category === 'Psychological Assessment') {
              await db.psychologicalAssessment.create({
                data: {
                  assessmentId: assessment.id,
                  testName: testResult.testName,
                  datePerformed: testResult.datePerformed,
                  score: testResult.result,
                  interpretation: testResult.normalRange,
                  notes: testResult.notes
                }
              })
            } else {
              await db.laboratoryTest.create({
                data: {
                  assessmentId: assessment.id,
                  testName: testResult.testName,
                  category: testResult.category,
                  datePerformed: testResult.datePerformed,
                  result: testResult.result,
                  normalRange: testResult.normalRange,
                  notes: testResult.notes
                }
              })
            }
          }
        }
      }
    }

    // Create mental status exam if provided
    if (body.mentalStatusExam && Object.keys(body.mentalStatusExam).length > 0) {
      await db.mentalStatusExam.create({
        data: {
          assessmentId: assessment.id,
          ...body.mentalStatusExam
        }
      })
    }

    // Handle symptoms (enhanced deduplication to prevent P2002 unique constraint errors)
    if (body.symptoms) {
      // Robust deduplication: filter out null/undefined/empty values and remove duplicates
      const selected = Array.isArray(body.symptoms.selectedSymptoms)
        ? Array.from(new Set<string>(
            body.symptoms.selectedSymptoms
              .filter(symptom => symptom && typeof symptom === 'string' && symptom.trim().length > 0)
              .map(symptom => symptom.trim())
          ))
        : []

      console.log(`Processing ${selected.length} unique symptoms for assessment ${assessment.id}`)

      // Clear existing symptom assessments for this assessment to keep in sync with current selection
      await db.symptomAssessment.deleteMany({ where: { assessmentId: assessment.id } })

      // Process symptoms with enhanced error handling
      for (const symptomName of selected) {
        try {
          // Find or create symptom with upsert to handle race conditions
          const symptom = await db.symptom.upsert({
            where: { name: symptomName },
            update: {}, // Don't update existing symptoms
            create: {
              name: symptomName,
              category: 'Other',
              description: ''
            }
          })

          // Create symptom assessment with additional validation
          const symptomDetails = body.symptoms.symptomDetails?.[symptomName] || {}
          await db.symptomAssessment.create({
            data: {
              assessmentId: assessment.id,
              symptomId: symptom.id,
              severity: symptomDetails.severity || null,
              duration: symptomDetails.duration || null,
              frequency: symptomDetails.frequency || null,
              notes: symptomDetails.notes || null
            }
          })
        } catch (symptomError) {
          console.error(`Error processing symptom "${symptomName}":`, symptomError)
          // Continue processing other symptoms instead of failing completely
          continue
        }
      }
    }

    // Handle diagnoses
    if (body.diagnosis) {
      // Primary diagnosis
      if (body.diagnosis.primaryDiagnosis && body.diagnosis.primaryDiagnosisCode) {
        let diagnosis = await db.diagnosis.findFirst({
          where: { code: body.diagnosis.primaryDiagnosisCode }
        })

        if (!diagnosis) {
          diagnosis = await db.diagnosis.create({
            data: {
              code: body.diagnosis.primaryDiagnosisCode,
              name: body.diagnosis.primaryDiagnosis,
              category: 'Other',
              description: ''
            }
          })
        }

        await db.diagnosisAssessment.create({
          data: {
            assessmentId: assessment.id,
            diagnosisId: diagnosis.id,
            type: 'primary',
            confidence: 'definite'
          }
        })
      }

      // Secondary diagnoses
      if (body.diagnosis.secondaryDiagnoses && body.diagnosis.secondaryDiagnoses.length > 0) {
        for (const secDiag of body.diagnosis.secondaryDiagnoses) {
          if (secDiag.diagnosis && secDiag.code) {
            let diagnosis = await db.diagnosis.findFirst({
              where: { code: secDiag.code }
            })

            if (!diagnosis) {
              diagnosis = await db.diagnosis.create({
                data: {
                  code: secDiag.code,
                  name: secDiag.diagnosis,
                  category: 'Other',
                  description: ''
                }
              })
            }

            await db.diagnosisAssessment.create({
              data: {
                assessmentId: assessment.id,
                diagnosisId: diagnosis.id,
                type: secDiag.type || 'secondary',
                confidence: 'probable'
              }
            })
          }
        }
      }
    }

    // Handle laboratory tests
    if (body.laboratoryTests && body.laboratoryTests.testResults && Array.isArray(body.laboratoryTests.testResults)) {
      // Delete existing test results for this assessment
      await db.laboratoryTest.deleteMany({
        where: { assessmentId: assessment.id }
      })
      await db.psychologicalAssessment.deleteMany({
        where: { assessmentId: assessment.id }
      })

      // Create new test results
      for (const testResult of body.laboratoryTests.testResults) {
        if (testResult.category === 'Psychological Assessments') {
          await db.psychologicalAssessment.create({
            data: {
              assessmentId: assessment.id,
              testName: testResult.testName,
              datePerformed: testResult.datePerformed,
              score: testResult.resultValue,
              interpretation: testResult.interpretation,
              notes: testResult.notes
            }
          })
        } else {
          await db.laboratoryTest.create({
            data: {
              assessmentId: assessment.id,
              testName: testResult.testName,
              category: testResult.category,
              datePerformed: testResult.datePerformed,
              result: testResult.resultValue,
              normalRange: testResult.normalRange,
              notes: testResult.notes
            }
          })
        }
      }
    }

    return NextResponse.json({ 
      success: true, 
      assessmentId: assessment.id 
    })

  } catch (error) {
    console.error('Error saving assessment:', error)
    return NextResponse.json(
      { error: 'Failed to save assessment' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const assessmentId = searchParams.get('id')

    if (assessmentId) {
      // Get specific assessment
      const assessment = await db.assessment.findUnique({
        where: { id: assessmentId },
        include: {
          demographics: true,
          riskAssessment: true,
          medicalHistory: true,
          mentalStatusExam: true,
          symptoms: {
            include: {
              symptom: true
            }
          },
          diagnoses: {
            include: {
              diagnosis: true
            }
          },
          psychiatricEpisodes: true,
          medicationHistory: true,
          laboratoryTests: true,
          psychologicalAssessments: true
        }
      })

      if (!assessment) {
        return NextResponse.json(
          { error: 'Assessment not found' },
          { status: 404 }
        )
      }

      // Reconstruct structured medical history data
      if (assessment.medicalHistory) {
        // Parse JSON fields back to objects
        if (assessment.medicalHistory.structuredMedicalConditions) {
          try {
            (assessment.medicalHistory as any).structuredMedicalConditions = JSON.parse(assessment.medicalHistory.structuredMedicalConditions)
          } catch (e) {
            (assessment.medicalHistory as any).structuredMedicalConditions = {}
          }
        }

        if (assessment.medicalHistory.substanceUseHistory) {
          try {
            (assessment.medicalHistory as any).substanceUseHistory = JSON.parse(assessment.medicalHistory.substanceUseHistory)
          } catch (e) {
            (assessment.medicalHistory as any).substanceUseHistory = []
          }
        }

        // Add structured data to medical history
        const medicalHistoryAny = assessment.medicalHistory as any
        medicalHistoryAny.psychiatricEpisodes = assessment.psychiatricEpisodes?.map(episode => ({
          ...episode,
          treatmentReceived: episode.treatmentReceived ? JSON.parse(episode.treatmentReceived) : []
        })) || []

        medicalHistoryAny.medicationHistory = assessment.medicationHistory || []

        // Reconstruct tests data
        const testResults = [
          ...(assessment.laboratoryTests?.map(test => ({
            testName: test.testName,
            category: test.category,
            datePerformed: test.datePerformed,
            result: test.result,
            normalRange: test.normalRange,
            notes: test.notes
          })) || []),
          ...(assessment.psychologicalAssessments?.map(test => ({
            testName: test.testName,
            category: 'Psychological Assessment',
            datePerformed: test.datePerformed,
            result: test.score,
            normalRange: test.interpretation,
            notes: test.notes
          })) || [])
        ]

        medicalHistoryAny.testsData = {
          laboratoryTests: {},
          psychologicalTests: {},
          testResults
        }
      }

      return NextResponse.json(assessment)
    } else {
      // Get all assessments
      const assessments = await db.assessment.findMany({
        include: {
          demographics: true,
          _count: {
            select: {
              symptoms: true,
              diagnoses: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      })

      return NextResponse.json(assessments)
    }

  } catch (error) {
    console.error('Error fetching assessments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch assessments' },
      { status: 500 }
    )
  }
}
