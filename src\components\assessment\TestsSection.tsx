"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ChevronDown, ChevronRight } from "lucide-react"

// Laboratory test categories and subcategories
const LABORATORY_TESTS = {
  'Complete Blood Count (CBC)': [
    'White Blood Cell Count (WBC)',
    'Hemoglobin (Hb)',
    'Platelet Count (Plt)',
    'Neutrophils',
    'Lymphocytes',
    'Monocytes',
    'Eosinophils',
    'Basophils'
  ],
  'Liver Function Tests (LFT)': [
    'AST (Aspartate Aminotransferase)',
    'ALT (Alanine Aminotransferase)',
    'Alkaline Phosphatase (Alk Phos)',
    'Total Bilirubin',
    'Direct Bilirubin'
  ],
  'Thyroid Function Tests (TFT)': [
    'TSH (Thyroid Stimulating Hormone)',
    'Free T4',
    'Free T3'
  ],
  'Metabolic Panel': [
    'Fasting Blood Sugar (FBS)',
    'HbA1c',
    'Total Cholesterol',
    'LDL Cholesterol',
    'HDL Cholesterol',
    'Triglycerides'
  ],
  'Renal Function Tests (RFT) & Electrolytes': [
    'Creatinine',
    'Blood Urea Nitrogen (BUN)',
    'Sodium',
    'Potassium',
    'Calcium',
    'Phosphor'
  ],
  'Urinalysis & Toxicology': [
    'Urinalysis (U/A)',
    'Urine Toxicology Screen'
  ],
  'Specialized Tests': [
    'Lithium Level',
    'Beta-hCG (Pregnancy Test)',
    'Vitamin B12',
    'Folate'
  ]
}

const PSYCHOLOGICAL_TESTS = [
  'DASS-21 (Depression, Anxiety, Stress Scale)',
  'MOCA (Montreal Cognitive Assessment)',
  'PHQ-9 (Patient Health Questionnaire)',
  'GAD-7 (Generalized Anxiety Disorder Scale)',
  'Y-BOCS (OCD Scale)'
]

interface TestResult {
  testName: string
  category: string
  datePerformed: string
  result: string
  normalRange: string
  notes: string
}

interface TestsData {
  laboratoryTests: { [key: string]: boolean }
  psychologicalTests: { [key: string]: boolean }
  testResults: TestResult[]
}

interface TestsSectionProps {
  data: TestsData
  onUpdate: (data: TestsData) => void
}

export default function TestsSection({ data, onUpdate }: TestsSectionProps) {
  const [testsData, setTestsData] = useState<TestsData>(data || {
    laboratoryTests: {},
    psychologicalTests: {},
    testResults: []
  })
  const [expandedCategories, setExpandedCategories] = useState<{ [key: string]: boolean }>({})

  useEffect(() => {
    onUpdate(testsData)
  }, [testsData])

  const toggleCategory = (category: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }))
  }

  const handleLabTestChange = (testName: string, checked: boolean) => {
    setTestsData(prev => ({
      ...prev,
      laboratoryTests: {
        ...prev.laboratoryTests,
        [testName]: checked
      }
    }))
  }

  const handlePsychTestChange = (testName: string, checked: boolean) => {
    setTestsData(prev => ({
      ...prev,
      psychologicalTests: {
        ...prev.psychologicalTests,
        [testName]: checked
      }
    }))
  }

  const addTestResult = (testName: string, category: string) => {
    const newResult: TestResult = {
      testName,
      category,
      datePerformed: '',
      result: '',
      normalRange: '',
      notes: ''
    }

    setTestsData(prev => ({
      ...prev,
      testResults: [...prev.testResults, newResult]
    }))
  }

  const updateTestResult = (index: number, field: keyof TestResult, value: string) => {
    setTestsData(prev => ({
      ...prev,
      testResults: prev.testResults.map((result, i) => 
        i === index ? { ...result, [field]: value } : result
      )
    }))
  }

  const removeTestResult = (index: number) => {
    setTestsData(prev => ({
      ...prev,
      testResults: prev.testResults.filter((_, i) => i !== index)
    }))
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Laboratory and Assessment Tests</CardTitle>
        <CardDescription>
          Track laboratory tests and psychological assessments performed
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Laboratory Tests */}
        <div className="space-y-4">
          <h4 className="font-medium text-base">Laboratory Tests</h4>
          {Object.entries(LABORATORY_TESTS).map(([category, tests]) => (
            <div key={category} className="border rounded-lg">
              <div 
                className="flex items-center justify-between p-3 cursor-pointer hover:bg-slate-50"
                onClick={() => toggleCategory(category)}
              >
                <div className="flex items-center space-x-2">
                  {expandedCategories[category] ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                  <Label className="font-medium cursor-pointer">{category}</Label>
                </div>
              </div>
              
              {expandedCategories[category] && (
                <div className="px-6 pb-3 space-y-2">
                  {tests.map((test) => (
                    <div key={test} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={test}
                          checked={testsData.laboratoryTests[test] || false}
                          onCheckedChange={(checked) => handleLabTestChange(test, checked as boolean)}
                        />
                        <Label htmlFor={test} className="text-sm cursor-pointer">
                          {test}
                        </Label>
                      </div>
                      {testsData.laboratoryTests[test] && (
                        <button
                          onClick={() => addTestResult(test, category)}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          Add Result
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Psychological Assessment Tests */}
        <div className="space-y-4">
          <h4 className="font-medium text-base">Psychological Assessment Tests</h4>
          <div className="border rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {PSYCHOLOGICAL_TESTS.map((test) => (
                <div key={test} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={test}
                      checked={testsData.psychologicalTests[test] || false}
                      onCheckedChange={(checked) => handlePsychTestChange(test, checked as boolean)}
                    />
                    <Label htmlFor={test} className="text-sm cursor-pointer">
                      {test}
                    </Label>
                  </div>
                  {testsData.psychologicalTests[test] && (
                    <button
                      onClick={() => addTestResult(test, 'Psychological Assessment')}
                      className="text-xs text-blue-600 hover:text-blue-800"
                    >
                      Add Result
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Test Results */}
        {testsData.testResults.length > 0 && (
          <div className="space-y-4">
            <h4 className="font-medium text-base">Test Results</h4>
            {testsData.testResults.map((result, index) => (
              <Card key={index} className="border-l-4 border-l-orange-500">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h5 className="font-medium">{result.testName}</h5>
                      <p className="text-sm text-slate-600">{result.category}</p>
                    </div>
                    <button
                      onClick={() => removeTestResult(index)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Remove
                    </button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Date Performed</Label>
                      <Input
                        value={result.datePerformed}
                        onChange={(e) => updateTestResult(index, 'datePerformed', e.target.value)}
                        type="date"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Result</Label>
                      <Input
                        value={result.result}
                        onChange={(e) => updateTestResult(index, 'result', e.target.value)}
                        placeholder="Test result value"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Normal Range</Label>
                      <Input
                        value={result.normalRange}
                        onChange={(e) => updateTestResult(index, 'normalRange', e.target.value)}
                        placeholder="Normal range for reference"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Notes</Label>
                    <Input
                      value={result.notes}
                      onChange={(e) => updateTestResult(index, 'notes', e.target.value)}
                      placeholder="Additional notes about the test result"
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {testsData.testResults.length === 0 && (
          <div className="text-center py-8 text-slate-500">
            <p>No test results recorded.</p>
            <p className="text-sm">Select tests above and click &quot;Add Result&quot; to record test outcomes.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export type { TestsData, TestResult }
