{"mappings": ";ACMA;IACE,QAAQ,EAAE,MAAM,YAAY,GAAG,CAAC,CAAC,KAAK,EAAE;QAAE,OAAO,EAAE,OAAO,CAAA;KAAE,KAAK,MAAM,YAAY,CAAC,CAAC;IACrF,OAAO,EAAE,OAAO,CAAC;CAClB;AAED,OAAA,MAAM,UAAU,MAAM,EAAE,CAAC,aAAa,CAarC,CAAC", "sources": ["packages/react/presence/src/packages/react/presence/src/useStateMachine.tsx", "packages/react/presence/src/packages/react/presence/src/Presence.tsx", "packages/react/presence/src/packages/react/presence/src/index.ts", "packages/react/presence/src/index.ts"], "sourcesContent": [null, null, null, "export { Presence } from './Presence';\nexport type { PresenceProps } from './Presence';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}