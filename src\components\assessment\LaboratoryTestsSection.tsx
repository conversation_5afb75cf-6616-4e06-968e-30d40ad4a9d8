"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Plus, Trash2 } from "lucide-react"

// Hierarchical test structure
const TEST_CATEGORIES = {
  "Blood Tests": [
    "CBC (Complete Blood Count)",
    "Comprehensive Metabolic Panel",
    "Lipid Panel",
    "TSH (Thyroid Stimulating Hormone)",
    "T3 (Triiodothyronine)",
    "T4 (Thyroxine)",
    "Vitamin B12",
    "Vitamin D",
    "Folate"
  ],
  "Imaging Studies": [
    "MRI Brain",
    "CT Head",
    "PET Scan",
    "X-Ray Chest"
  ],
  "Psychological Assessments": [
    "MMSE (Mini-Mental State Exam)",
    "PHQ-9 (Patient Health Questionnaire)",
    "GAD-7 (Generalized Anxiety Disorder Scale)",
    "AUDIT (Alcohol Use Disorders Test)",
    "Beck Depression Inventory"
  ],
  "Neurological Tests": [
    "EEG (Electroencephalogram)",
    "EMG (Electromyography)",
    "Nerve Conduction Studies"
  ]
}

interface TestResult {
  id: string
  testName: string
  category: string
  datePerformed: string
  resultValue: string
  normalRange: string
  interpretation: string
  notes: string
}

interface LaboratoryTestsData {
  selectedTests?: Record<string, boolean>
  testResults?: TestResult[]
}

interface LaboratoryTestsSectionProps {
  data: LaboratoryTestsData
  onUpdate: (data: LaboratoryTestsData) => void
}

export default function LaboratoryTestsSection({ data, onUpdate }: LaboratoryTestsSectionProps) {
  const [formData, setFormData] = useState<LaboratoryTestsData>(data || {
    selectedTests: {},
    testResults: []
  })

  useEffect(() => {
    onUpdate(formData)
  }, [formData, onUpdate])

  const handleTestSelection = (testName: string, category: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      selectedTests: {
        ...prev.selectedTests,
        [testName]: checked
      }
    }))

    // If checked, add a new test result entry
    if (checked) {
      addTestResult(testName, category)
    } else {
      // If unchecked, remove the test result entry
      setFormData(prev => ({
        ...prev,
        testResults: prev.testResults?.filter(result => result.testName !== testName) || []
      }))
    }
  }

  const addTestResult = (testName: string, category: string) => {
    const newResult: TestResult = {
      id: Date.now().toString(),
      testName,
      category,
      datePerformed: '',
      resultValue: '',
      normalRange: '',
      interpretation: '',
      notes: ''
    }

    setFormData(prev => ({
      ...prev,
      testResults: [...(prev.testResults || []), newResult]
    }))
  }

  const updateTestResult = (id: string, field: keyof TestResult, value: string) => {
    setFormData(prev => ({
      ...prev,
      testResults: prev.testResults?.map(result => 
        result.id === id ? { ...result, [field]: value } : result
      ) || []
    }))
  }

  const removeTestResult = (id: string, testName: string) => {
    setFormData(prev => ({
      ...prev,
      selectedTests: {
        ...prev.selectedTests,
        [testName]: false
      },
      testResults: prev.testResults?.filter(result => result.id !== id) || []
    }))
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Laboratory and Assessment Tests</h2>
        <p className="text-sm text-slate-600">Select tests performed and enter results with interpretations.</p>
      </div>

      {/* Test Selection by Category */}
      {Object.entries(TEST_CATEGORIES).map(([category, tests]) => (
        <Card key={category}>
          <CardHeader>
            <CardTitle className="text-lg">{category}</CardTitle>
            <CardDescription>Select tests that have been performed</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {tests.map((testName) => {
                const isSelected = formData.selectedTests?.[testName] || false
                return (
                  <div key={testName} className="flex items-center space-x-2">
                    <Checkbox
                      id={`${category}-${testName}`}
                      checked={isSelected}
                      onCheckedChange={(checked) => handleTestSelection(testName, category, checked as boolean)}
                    />
                    <Label htmlFor={`${category}-${testName}`} className="text-sm font-normal cursor-pointer">
                      {testName}
                    </Label>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      ))}

      {/* Test Results */}
      {formData.testResults && formData.testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Test Results</CardTitle>
            <CardDescription>Enter detailed results for selected tests</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {formData.testResults.map((result) => (
              <Card key={result.id} className="border-l-4 border-l-blue-500">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{result.category}</Badge>
                      <span className="font-medium">{result.testName}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeTestResult(result.id, result.testName)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Test Date</Label>
                      <Input
                        type="date"
                        value={result.datePerformed}
                        onChange={(e) => updateTestResult(result.id, 'datePerformed', e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Result Value</Label>
                      <Input
                        value={result.resultValue}
                        onChange={(e) => updateTestResult(result.id, 'resultValue', e.target.value)}
                        placeholder="Enter test result"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Normal Range</Label>
                      <Input
                        value={result.normalRange}
                        onChange={(e) => updateTestResult(result.id, 'normalRange', e.target.value)}
                        placeholder="e.g., 4.5-11.0"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Interpretation</Label>
                    <Textarea
                      value={result.interpretation}
                      onChange={(e) => updateTestResult(result.id, 'interpretation', e.target.value)}
                      placeholder="Clinical interpretation of results"
                      rows={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Additional Notes</Label>
                    <Textarea
                      value={result.notes}
                      onChange={(e) => updateTestResult(result.id, 'notes', e.target.value)}
                      placeholder="Any additional notes about the test or results"
                      rows={2}
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </CardContent>
        </Card>
      )}

      {(!formData.testResults || formData.testResults.length === 0) && (
        <div className="text-center py-8 text-slate-500">
          <p>No tests selected.</p>
          <p className="text-sm">Select tests from the categories above to enter results.</p>
        </div>
      )}
    </div>
  )
}

export type { LaboratoryTestsData, TestResult }
