"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Plus, Trash2 } from "lucide-react"

// Enhanced hierarchical test structure with conditional displays
const TEST_CATEGORIES = {
  "Blood Tests": [
    "CBC (Complete Blood Count)",
    "Comprehensive Metabolic Panel",
    "Lipid Panel",
    "TSH (Thyroid Stimulating Hormone)",
    "T3 (Triiodothyronine)",
    "T4 (Thyroxine)",
    "Vitamin B12",
    "Vitamin D",
    "Folate"
  ],
  "Imaging Studies": [
    "MRI Brain",
    "CT Head",
    "PET Scan",
    "X-Ray Chest"
  ],
  "Psychological Assessments": [
    "MMSE (Mini-Mental State Exam)",
    "PHQ-9 (Patient Health Questionnaire)",
    "GAD-7 (Generalized Anxiety Disorder Scale)",
    "AUDIT (Alcohol Use Disorders Test)",
    "Beck Depression Inventory"
  ],
  "Neurological Tests": [
    "EEG (Electroencephalogram)",
    "EMG (Electromyography)",
    "Nerve Conduction Studies"
  ]
}

// CBC component values
const CBC_COMPONENTS = [
  { name: "WBC", label: "White Blood Cells", unit: "K/μL", normalRange: "4.5-11.0" },
  { name: "Hemoglobin", label: "Hemoglobin", unit: "g/dL", normalRange: "12.0-16.0" },
  { name: "Platelets", label: "Platelets", unit: "K/μL", normalRange: "150-450" },
  { name: "Neutrophils", label: "Neutrophils", unit: "%", normalRange: "50-70" },
  { name: "Lymphocytes", label: "Lymphocytes", unit: "%", normalRange: "20-40" },
  { name: "Monocytes", label: "Monocytes", unit: "%", normalRange: "2-8" },
  { name: "Eosinophils", label: "Eosinophils", unit: "%", normalRange: "1-4" },
  { name: "Basophils", label: "Basophils", unit: "%", normalRange: "0-2" }
]

// Psychological assessment questionnaires
const PSYCHOLOGICAL_QUESTIONNAIRES = {
  "PHQ-9 (Patient Health Questionnaire)": {
    questions: [
      "Little interest or pleasure in doing things",
      "Feeling down, depressed, or hopeless",
      "Trouble falling or staying asleep, or sleeping too much",
      "Feeling tired or having little energy",
      "Poor appetite or overeating",
      "Feeling bad about yourself or that you are a failure",
      "Trouble concentrating on things",
      "Moving or speaking slowly or being fidgety/restless",
      "Thoughts that you would be better off dead or hurting yourself"
    ],
    scoring: "0-4: Minimal depression, 5-9: Mild depression, 10-14: Moderate depression, 15-19: Moderately severe depression, 20-27: Severe depression"
  },
  "GAD-7 (Generalized Anxiety Disorder Scale)": {
    questions: [
      "Feeling nervous, anxious, or on edge",
      "Not being able to stop or control worrying",
      "Worrying too much about different things",
      "Trouble relaxing",
      "Being so restless that it's hard to sit still",
      "Becoming easily annoyed or irritable",
      "Feeling afraid as if something awful might happen"
    ],
    scoring: "0-4: Minimal anxiety, 5-9: Mild anxiety, 10-14: Moderate anxiety, 15-21: Severe anxiety"
  },
  "MMSE (Mini-Mental State Exam)": {
    questions: [
      "Orientation to time (year, season, date, day, month)",
      "Orientation to place (state, county, town, hospital, floor)",
      "Registration (repeat 3 words)",
      "Attention and calculation (serial 7s or spell WORLD backwards)",
      "Recall (remember the 3 words)",
      "Language (naming, repetition, comprehension, reading, writing)",
      "Visuospatial (copy intersecting pentagons)"
    ],
    scoring: "24-30: Normal cognition, 18-23: Mild cognitive impairment, 0-17: Severe cognitive impairment"
  }
}

interface TestResult {
  id: string
  testName: string
  category: string
  datePerformed: string
  resultValue: string
  normalRange: string
  interpretation: string
  notes: string
}

interface CBCComponent {
  name: string
  value: string
  unit: string
  normalRange: string
}

interface PsychologicalAssessmentResult {
  testName: string
  responses: Record<string, number>
  totalScore: number
  interpretation: string
  isCompleted: boolean
}

interface ImagingStudyResult {
  testName: string
  findings: string
  impression: string
  recommendations: string
}

interface NeurologicalTestResult {
  testName: string
  findings: string
  interpretation: string
  recommendations: string
}

interface LaboratoryTestsData {
  selectedTests?: Record<string, boolean>
  testResults?: TestResult[]
  cbcComponents?: Record<string, CBCComponent>
  psychologicalAssessments?: Record<string, PsychologicalAssessmentResult>
  imagingStudies?: Record<string, ImagingStudyResult>
  neurologicalTests?: Record<string, NeurologicalTestResult>
}

interface LaboratoryTestsSectionProps {
  data: LaboratoryTestsData
  onUpdate: (data: LaboratoryTestsData) => void
}

export default function LaboratoryTestsSection({ data, onUpdate }: LaboratoryTestsSectionProps) {
  const [formData, setFormData] = useState<LaboratoryTestsData>(data || {
    selectedTests: {},
    testResults: [],
    cbcComponents: {},
    psychologicalAssessments: {},
    imagingStudies: {},
    neurologicalTests: {}
  })

  useEffect(() => {
    onUpdate(formData)
  }, [formData, onUpdate])

  const handleTestSelection = (testName: string, category: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      selectedTests: {
        ...prev.selectedTests,
        [testName]: checked
      }
    }))

    // If checked, add a new test result entry
    if (checked) {
      addTestResult(testName, category)
    } else {
      // If unchecked, remove the test result entry
      setFormData(prev => ({
        ...prev,
        testResults: prev.testResults?.filter(result => result.testName !== testName) || []
      }))
    }
  }

  const addTestResult = (testName: string, category: string) => {
    const newResult: TestResult = {
      id: Date.now().toString(),
      testName,
      category,
      datePerformed: '',
      resultValue: '',
      normalRange: '',
      interpretation: '',
      notes: ''
    }

    setFormData(prev => ({
      ...prev,
      testResults: [...(prev.testResults || []), newResult]
    }))
  }

  const updateTestResult = (id: string, field: keyof TestResult, value: string) => {
    setFormData(prev => ({
      ...prev,
      testResults: prev.testResults?.map(result => 
        result.id === id ? { ...result, [field]: value } : result
      ) || []
    }))
  }

  const removeTestResult = (id: string, testName: string) => {
    setFormData(prev => ({
      ...prev,
      selectedTests: {
        ...prev.selectedTests,
        [testName]: false
      },
      testResults: prev.testResults?.filter(result => result.id !== id) || []
    }))
  }

  // CBC component handlers
  const handleCBCComponentUpdate = (componentName: string, field: keyof CBCComponent, value: string) => {
    setFormData(prev => ({
      ...prev,
      cbcComponents: {
        ...prev.cbcComponents,
        [componentName]: {
          ...prev.cbcComponents?.[componentName],
          [field]: value
        } as CBCComponent
      }
    }))
  }

  // Psychological assessment handlers
  const handlePsychologicalResponse = (testName: string, questionIndex: number, score: number) => {
    setFormData(prev => {
      const currentAssessment = prev.psychologicalAssessments?.[testName] || {
        testName,
        responses: {},
        totalScore: 0,
        interpretation: '',
        isCompleted: false
      }

      const updatedResponses = {
        ...currentAssessment.responses,
        [questionIndex]: score
      }

      const totalScore = Object.values(updatedResponses).reduce((sum, score) => sum + score, 0)

      return {
        ...prev,
        psychologicalAssessments: {
          ...prev.psychologicalAssessments,
          [testName]: {
            ...currentAssessment,
            responses: updatedResponses,
            totalScore
          }
        }
      }
    })
  }

  const completePsychologicalAssessment = (testName: string) => {
    setFormData(prev => {
      const assessment = prev.psychologicalAssessments?.[testName]
      if (!assessment) return prev

      const questionnaire = PSYCHOLOGICAL_QUESTIONNAIRES[testName as keyof typeof PSYCHOLOGICAL_QUESTIONNAIRES]
      let interpretation = ''

      if (testName === 'PHQ-9 (Patient Health Questionnaire)') {
        if (assessment.totalScore <= 4) interpretation = 'Minimal depression'
        else if (assessment.totalScore <= 9) interpretation = 'Mild depression'
        else if (assessment.totalScore <= 14) interpretation = 'Moderate depression'
        else if (assessment.totalScore <= 19) interpretation = 'Moderately severe depression'
        else interpretation = 'Severe depression'
      } else if (testName === 'GAD-7 (Generalized Anxiety Disorder Scale)') {
        if (assessment.totalScore <= 4) interpretation = 'Minimal anxiety'
        else if (assessment.totalScore <= 9) interpretation = 'Mild anxiety'
        else if (assessment.totalScore <= 14) interpretation = 'Moderate anxiety'
        else interpretation = 'Severe anxiety'
      } else if (testName === 'MMSE (Mini-Mental State Exam)') {
        if (assessment.totalScore >= 24) interpretation = 'Normal cognition'
        else if (assessment.totalScore >= 18) interpretation = 'Mild cognitive impairment'
        else interpretation = 'Severe cognitive impairment'
      }

      return {
        ...prev,
        psychologicalAssessments: {
          ...prev.psychologicalAssessments,
          [testName]: {
            ...assessment,
            interpretation,
            isCompleted: true
          }
        }
      }
    })
  }

  // Imaging study handlers
  const handleImagingStudyUpdate = (testName: string, field: keyof ImagingStudyResult, value: string) => {
    setFormData(prev => ({
      ...prev,
      imagingStudies: {
        ...prev.imagingStudies,
        [testName]: {
          ...prev.imagingStudies?.[testName],
          testName,
          [field]: value
        } as ImagingStudyResult
      }
    }))
  }

  // Neurological test handlers
  const handleNeurologicalTestUpdate = (testName: string, field: keyof NeurologicalTestResult, value: string) => {
    setFormData(prev => ({
      ...prev,
      neurologicalTests: {
        ...prev.neurologicalTests,
        [testName]: {
          ...prev.neurologicalTests?.[testName],
          testName,
          [field]: value
        } as NeurologicalTestResult
      }
    }))
  }

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-slate-900 mb-3">Laboratory and Assessment Tests</h2>
        <p className="text-base text-slate-600 leading-relaxed">Select tests performed and enter detailed results with clinical interpretations.</p>
      </div>

      {/* Enhanced Test Selection by Category */}
      {Object.entries(TEST_CATEGORIES).map(([category, tests]) => (
        <Card key={category} className="form-card-enhanced">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-semibold text-slate-800">{category}</CardTitle>
            <CardDescription className="text-slate-600">Select tests that have been performed for this patient</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {tests.map((testName) => {
                const isSelected = formData.selectedTests?.[testName] || false
                return (
                  <div key={testName} className="flex items-center space-x-3 p-2 rounded-md hover:bg-slate-50 transition-colors duration-200">
                    <Checkbox
                      id={`${category}-${testName}`}
                      checked={isSelected}
                      onCheckedChange={(checked) => handleTestSelection(testName, category, checked as boolean)}
                      className="symptom-checkbox"
                    />
                    <Label htmlFor={`${category}-${testName}`} className="symptom-label flex-1">
                      {testName}
                    </Label>
                  </div>
                )
              })}
            </div>

            {/* Conditional displays based on selected tests */}
            {category === "Blood Tests" && formData.selectedTests?.["CBC (Complete Blood Count)"] && (
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="text-lg font-semibold text-blue-900 mb-4">CBC Components</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {CBC_COMPONENTS.map((component) => (
                    <div key={component.name} className="space-y-2">
                      <Label className="form-label-enhanced">{component.label}</Label>
                      <Input
                        value={formData.cbcComponents?.[component.name]?.value || ''}
                        onChange={(e) => handleCBCComponentUpdate(component.name, 'value', e.target.value)}
                        placeholder={`${component.normalRange} ${component.unit}`}
                        className="form-input-enhanced"
                      />
                      <p className="text-xs text-slate-500">Normal: {component.normalRange} {component.unit}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {category === "Imaging Studies" && tests.some(test => formData.selectedTests?.[test]) && (
              <div className="mt-6 space-y-4">
                {tests.filter(test => formData.selectedTests?.[test]).map((testName) => (
                  <div key={testName} className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h4 className="text-lg font-semibold text-green-900 mb-4">{testName} - Report</h4>
                    <div className="space-y-4">
                      <div>
                        <Label className="form-label-enhanced">Findings</Label>
                        <Textarea
                          value={formData.imagingStudies?.[testName]?.findings || ''}
                          onChange={(e) => handleImagingStudyUpdate(testName, 'findings', e.target.value)}
                          placeholder="Describe the imaging findings..."
                          rows={3}
                          className="form-input-enhanced"
                        />
                      </div>
                      <div>
                        <Label className="form-label-enhanced">Impression</Label>
                        <Textarea
                          value={formData.imagingStudies?.[testName]?.impression || ''}
                          onChange={(e) => handleImagingStudyUpdate(testName, 'impression', e.target.value)}
                          placeholder="Clinical impression based on findings..."
                          rows={2}
                          className="form-input-enhanced"
                        />
                      </div>
                      <div>
                        <Label className="form-label-enhanced">Recommendations</Label>
                        <Textarea
                          value={formData.imagingStudies?.[testName]?.recommendations || ''}
                          onChange={(e) => handleImagingStudyUpdate(testName, 'recommendations', e.target.value)}
                          placeholder="Clinical recommendations..."
                          rows={2}
                          className="form-input-enhanced"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {category === "Neurological Tests" && tests.some(test => formData.selectedTests?.[test]) && (
              <div className="mt-6 space-y-4">
                {tests.filter(test => formData.selectedTests?.[test]).map((testName) => (
                  <div key={testName} className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                    <h4 className="text-lg font-semibold text-purple-900 mb-4">{testName} - Results</h4>
                    <div className="space-y-4">
                      <div>
                        <Label className="form-label-enhanced">Test Findings</Label>
                        <Textarea
                          value={formData.neurologicalTests?.[testName]?.findings || ''}
                          onChange={(e) => handleNeurologicalTestUpdate(testName, 'findings', e.target.value)}
                          placeholder="Describe the neurological test findings..."
                          rows={3}
                          className="form-input-enhanced"
                        />
                      </div>
                      <div>
                        <Label className="form-label-enhanced">Clinical Interpretation</Label>
                        <Textarea
                          value={formData.neurologicalTests?.[testName]?.interpretation || ''}
                          onChange={(e) => handleNeurologicalTestUpdate(testName, 'interpretation', e.target.value)}
                          placeholder="Clinical interpretation of results..."
                          rows={2}
                          className="form-input-enhanced"
                        />
                      </div>
                      <div>
                        <Label className="form-label-enhanced">Recommendations</Label>
                        <Textarea
                          value={formData.neurologicalTests?.[testName]?.recommendations || ''}
                          onChange={(e) => handleNeurologicalTestUpdate(testName, 'recommendations', e.target.value)}
                          placeholder="Clinical recommendations based on results..."
                          rows={2}
                          className="form-input-enhanced"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      ))}

      {/* Psychological Assessment Questionnaires */}
      {Object.entries(PSYCHOLOGICAL_QUESTIONNAIRES).some(([testName]) => formData.selectedTests?.[testName]) && (
        <Card className="form-card-enhanced">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-semibold text-slate-800">Psychological Assessment Questionnaires</CardTitle>
            <CardDescription className="text-slate-600">Complete the selected psychological assessments</CardDescription>
          </CardHeader>
          <CardContent className="space-y-8">
            {Object.entries(PSYCHOLOGICAL_QUESTIONNAIRES).map(([testName, questionnaire]) => {
              if (!formData.selectedTests?.[testName]) return null

              const assessment = formData.psychologicalAssessments?.[testName]
              const isCompleted = assessment?.isCompleted || false

              return (
                <div key={testName} className="p-6 bg-amber-50 border border-amber-200 rounded-lg">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-semibold text-amber-900">{testName}</h4>
                    {isCompleted && (
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          Score: {assessment.totalScore}
                        </Badge>
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          {assessment.interpretation}
                        </Badge>
                      </div>
                    )}
                  </div>

                  {!isCompleted ? (
                    <div className="space-y-4">
                      <p className="text-sm text-amber-700 mb-4">
                        Rate each item: 0 = Not at all, 1 = Several days, 2 = More than half the days, 3 = Nearly every day
                      </p>
                      {questionnaire.questions.map((question, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-white rounded border">
                          <span className="text-sm font-medium text-slate-700 flex-1">{question}</span>
                          <div className="flex space-x-2 ml-4">
                            {[0, 1, 2, 3].map((score) => (
                              <label key={score} className="flex items-center space-x-1 cursor-pointer">
                                <input
                                  type="radio"
                                  name={`${testName}-${index}`}
                                  value={score}
                                  checked={assessment?.responses?.[index] === score}
                                  onChange={() => handlePsychologicalResponse(testName, index, score)}
                                  className="w-4 h-4 text-blue-600"
                                />
                                <span className="text-sm text-slate-600">{score}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      ))}
                      <div className="flex justify-between items-center mt-6">
                        <p className="text-sm text-slate-600">
                          Current Score: {assessment?.totalScore || 0}
                        </p>
                        <Button
                          onClick={() => completePsychologicalAssessment(testName)}
                          disabled={!assessment?.responses || Object.keys(assessment.responses).length < questionnaire.questions.length}
                          className="bg-amber-600 hover:bg-amber-700"
                        >
                          Finish Assessment
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="p-4 bg-white rounded border">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-lg font-semibold text-slate-800">
                            Final Score: {assessment.totalScore}
                          </p>
                          <p className="text-base text-slate-600">
                            Interpretation: {assessment.interpretation}
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          onClick={() => {
                            setFormData(prev => ({
                              ...prev,
                              psychologicalAssessments: {
                                ...prev.psychologicalAssessments,
                                [testName]: {
                                  ...assessment,
                                  isCompleted: false
                                }
                              }
                            }))
                          }}
                          className="text-amber-700 border-amber-300 hover:bg-amber-50"
                        >
                          Edit Responses
                        </Button>
                      </div>
                      <div className="mt-3 text-xs text-slate-500">
                        <p>Scoring Guide: {questionnaire.scoring}</p>
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </CardContent>
        </Card>
      )}

      {/* Test Results */}
      {formData.testResults && formData.testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Test Results</CardTitle>
            <CardDescription>Enter detailed results for selected tests</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {formData.testResults.map((result) => (
              <Card key={result.id} className="border-l-4 border-l-blue-500">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{result.category}</Badge>
                      <span className="font-medium">{result.testName}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeTestResult(result.id, result.testName)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Test Date</Label>
                      <Input
                        type="date"
                        value={result.datePerformed}
                        onChange={(e) => updateTestResult(result.id, 'datePerformed', e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Result Value</Label>
                      <Input
                        value={result.resultValue}
                        onChange={(e) => updateTestResult(result.id, 'resultValue', e.target.value)}
                        placeholder="Enter test result"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Normal Range</Label>
                      <Input
                        value={result.normalRange}
                        onChange={(e) => updateTestResult(result.id, 'normalRange', e.target.value)}
                        placeholder="e.g., 4.5-11.0"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Interpretation</Label>
                    <Textarea
                      value={result.interpretation}
                      onChange={(e) => updateTestResult(result.id, 'interpretation', e.target.value)}
                      placeholder="Clinical interpretation of results"
                      rows={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Additional Notes</Label>
                    <Textarea
                      value={result.notes}
                      onChange={(e) => updateTestResult(result.id, 'notes', e.target.value)}
                      placeholder="Any additional notes about the test or results"
                      rows={2}
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </CardContent>
        </Card>
      )}

      {(!formData.testResults || formData.testResults.length === 0) && (
        <div className="text-center py-8 text-slate-500">
          <p>No tests selected.</p>
          <p className="text-sm">Select tests from the categories above to enter results.</p>
        </div>
      )}
    </div>
  )
}

export type { LaboratoryTestsData, TestResult }
