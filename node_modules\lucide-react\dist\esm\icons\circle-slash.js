/**
 * @license lucide-react v0.312.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const CircleSlash = createLucideIcon("CircleSlash", [
  ["line", { x1: "9", x2: "15", y1: "15", y2: "9", key: "1dfufj" }],
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }]
]);

export { CircleSlash as default };
//# sourceMappingURL=circle-slash.js.map
