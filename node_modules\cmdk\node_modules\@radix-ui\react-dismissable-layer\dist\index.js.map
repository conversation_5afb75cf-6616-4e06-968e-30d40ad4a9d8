{"mappings": ";;;;;;;;;;;;;;;;;;;A;;;;;;;ACSA;;oGAEA,CAEA,MAAMW,4CAAsB,GAAG,kBAA/B,AAAA;AACA,MAAMC,oCAAc,GAAG,yBAAvB,AAAA;AACA,MAAMC,0CAAoB,GAAG,qCAA7B,AAAA;AACA,MAAMC,mCAAa,GAAG,+BAAtB,AAAA;AAEA,IAAIC,+CAAJ,AAAA;AAEA,MAAMC,6CAAuB,GAAA,aAAGZ,CAAAA,0BAAA,CAAoB;IAClDc,MAAM,EAAE,IAAIC,GAAJ,EAD0C;IAElDC,sCAAsC,EAAE,IAAID,GAAJ,EAFU;IAGlDE,QAAQ,EAAE,IAAIF,GAAJ,EAAVE;CAH8B,CAAhC,AAAoD;AA0CpD,MAAMrB,yCAAgB,GAAA,aAAGI,CAAAA,uBAAA,CACvB,CAACmB,KAAD,EAAQC,YAAR,GAAyB;IACvB,MAAM,+BACJC,2BAA2B,GAAG,KAD1B,G,iBAEJC,eAFI,CAAA,E,sBAGJC,oBAHI,CAAA,E,gBAIJC,cAJI,CAAA,E,mBAKJC,iBALI,CAAA,E,WAMJC,SANI,CAAA,EAOJ,GAAGC,UAAH,EAPI,GAQFR,KARJ,AAAM;IASN,MAAMS,OAAO,GAAG5B,uBAAA,CAAiBY,6CAAjB,CAAhB,AAAA;IACA,MAAM,CAACkB,KAAD,EAAOC,OAAP,CAAA,GAAkB/B,qBAAA,CAA+C,IAA/C,CAAxB,AAAA;IACA,MAAM,GAAGiC,KAAH,CAAA,GAAYjC,qBAAA,CAAe,EAAf,CAAlB,AAAA;IACA,MAAMkC,YAAY,GAAG9B,8CAAe,CAACgB,YAAD,EAAgBU,CAAAA,IAAD,GAAUC,OAAO,CAACD,IAAD,CAAhC;IAAA,CAApC,AAAA;IACA,MAAMhB,MAAM,GAAGqB,KAAK,CAACC,IAAN,CAAWR,OAAO,CAACd,MAAnB,CAAf,AAAA;IACA,MAAM,CAACuB,4CAAD,CAAA,GAAiD;WAAIT,OAAO,CAACZ,sCAAZ;KAAA,CAAoDsB,KAApD,CAA0D,EAA1D,CAAvD,AAfuB,EAe+F,kBAAtH;IACA,MAAMC,iDAAiD,GAAGzB,MAAM,CAAC0B,OAAP,CAAeH,4CAAf,CAA1D,AAhBuB,EAgBiG,kBAAxH;IACA,MAAMI,KAAK,GAAGX,KAAI,GAAGhB,MAAM,CAAC0B,OAAP,CAAeV,KAAf,CAAH,GAA0B,EAA5C,AAAA;IACA,MAAMY,2BAA2B,GAAGd,OAAO,CAACZ,sCAAR,CAA+C2B,IAA/C,GAAsD,CAA1F,AAAA;IACA,MAAMC,sBAAsB,GAAGH,KAAK,IAAIF,iDAAxC,AAAA;IAEA,MAAMM,kBAAkB,GAAGC,2CAAqB,CAAEC,CAAAA,KAAD,GAAW;QAC1D,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAArB,AAAA;QACA,MAAMC,qBAAqB,GAAG;eAAIrB,OAAO,CAACX,QAAZ;SAAA,CAAsBiC,IAAtB,CAA4BC,CAAAA,MAAD,GAAYA,MAAM,CAACC,QAAP,CAAgBJ,MAAhB,CAAvC;QAAA,CAA9B,AAAA;QACA,IAAI,CAACJ,sBAAD,IAA2BK,qBAA/B,EAAsD,OAAtD;QACA1B,oBAAoB,KAAA,IAApB,IAAAA,oBAAoB,KAAA,KAAA,CAApB,IAAAA,oBAAoB,CAAGwB,KAAH,CAApB,CAAAxB;QACAE,iBAAiB,KAAA,IAAjB,IAAAA,iBAAiB,KAAA,KAAA,CAAjB,IAAAA,iBAAiB,CAAGsB,KAAH,CAAjB,CAAAtB;QACA,IAAI,CAACsB,KAAK,CAACM,gBAAX,EAA6B3B,SAAS,KAAA,IAAT,IAAAA,SAAS,KAAA,KAAA,CAAT,IAAAA,SAAS,EAAtC,CAAA;KAN8C,CAAhD,AAOC;IAED,MAAM4B,YAAY,GAAGC,qCAAe,CAAER,CAAAA,KAAD,GAAW;QAC9C,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAArB,AAAA;QACA,MAAMQ,eAAe,GAAG;eAAI5B,OAAO,CAACX,QAAZ;SAAA,CAAsBiC,IAAtB,CAA4BC,CAAAA,MAAD,GAAYA,MAAM,CAACC,QAAP,CAAgBJ,MAAhB,CAAvC;QAAA,CAAxB,AAAA;QACA,IAAIQ,eAAJ,EAAqB,OAArB;QACAhC,cAAc,KAAA,IAAd,IAAAA,cAAc,KAAA,KAAA,CAAd,IAAAA,cAAc,CAAGuB,KAAH,CAAd,CAAAvB;QACAC,iBAAiB,KAAA,IAAjB,IAAAA,iBAAiB,KAAA,KAAA,CAAjB,IAAAA,iBAAiB,CAAGsB,KAAH,CAAjB,CAAAtB;QACA,IAAI,CAACsB,KAAK,CAACM,gBAAX,EAA6B3B,SAAS,KAAA,IAAT,IAAAA,SAAS,KAAA,KAAA,CAAT,IAAAA,SAAS,EAAtC,CAAA;KANkC,CAApC,AAOC;IAEDpB,oDAAgB,CAAEyC,CAAAA,KAAD,GAAW;QAC1B,MAAMU,cAAc,GAAGhB,KAAK,KAAKb,OAAO,CAACd,MAAR,CAAe6B,IAAf,GAAsB,CAAvD,AAAA;QACA,IAAI,CAACc,cAAL,EAAqB,OAArB;QACAnC,eAAe,KAAA,IAAf,IAAAA,eAAe,KAAA,KAAA,CAAf,IAAAA,eAAe,CAAGyB,KAAH,CAAf,CAAAzB;QACA,IAAI,CAACyB,KAAK,CAACM,gBAAP,IAA2B3B,SAA/B,EAA0C;YACxCqB,KAAK,CAACW,cAAN,EAAAX,CAAAA;YACArB,SAAS,EAATA,CAAAA;SACD;KAPa,CAAhB,CAQC;IAED1B,sBAAA,CAAgB,IAAM;QACpB,IAAI,CAAC8B,KAAL,EAAW,OAAX;QACA,IAAIT,2BAAJ,EAAiC;YAC/B,IAAIO,OAAO,CAACZ,sCAAR,CAA+C2B,IAA/C,KAAwD,CAA5D,EAA+D;gBAC7DhC,+CAAyB,GAAGiD,QAAQ,CAACC,IAAT,CAAcC,KAAd,CAAoBC,aAAhD,CAAApD;gBACAiD,QAAQ,CAACC,IAAT,CAAcC,KAAd,CAAoBC,aAApB,GAAoC,MAApC,CAAAH;aACD;YACDhC,OAAO,CAACZ,sCAAR,CAA+CgD,GAA/C,CAAmDlC,KAAnD,CAAAF,CAAAA;SACD;QACDA,OAAO,CAACd,MAAR,CAAekD,GAAf,CAAmBlC,KAAnB,CAAAF,CAAAA;QACAqC,oCAAc,EAAdA,CAAAA;QACA,OAAO,IAAM;YACX,IACE5C,2BAA2B,IAC3BO,OAAO,CAACZ,sCAAR,CAA+C2B,IAA/C,KAAwD,CAF1D,EAIEiB,QAAQ,CAACC,IAAT,CAAcC,KAAd,CAAoBC,aAApB,GAAoCpD,+CAApC,CAAAiD;SALJ,CAOC;KAlBH,EAmBG;QAAC9B,KAAD;QAAOT,2BAAP;QAAoCO,OAApC;KAnBH,CAmBC,CAAA;IAED;;;;;KAKJ,CACI5B,sBAAA,CAAgB,IAAM;QACpB,OAAO,IAAM;YACX,IAAI,CAAC8B,KAAL,EAAW,OAAX;YACAF,OAAO,CAACd,MAAR,CAAeoD,MAAf,CAAsBpC,KAAtB,CAAAF,CAAAA;YACAA,OAAO,CAACZ,sCAAR,CAA+CkD,MAA/C,CAAsDpC,KAAtD,CAAAF,CAAAA;YACAqC,oCAAc,EAAdA,CAAAA;SAJF,CAKC;KANH,EAOG;QAACnC,KAAD;QAAOF,OAAP;KAPH,CAOC,CAAA;IAED5B,sBAAA,CAAgB,IAAM;QACpB,MAAMmE,YAAY,GAAG,IAAMlC,KAAK,CAAC,EAAD,CAAhC;QAAA;QACA2B,QAAQ,CAACQ,gBAAT,CAA0B5D,oCAA1B,EAA0C2D,YAA1C,CAAAP,CAAAA;QACA,OAAO,IAAMA,QAAQ,CAACS,mBAAT,CAA6B7D,oCAA7B,EAA6C2D,YAA7C,CAAb;QAAA,CAAA;KAHF,EAIG,EAJH,CAIC,CAAA;IAED,OAAA,aACE,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EAAA,2DAAA,CAAA,EAAA,EACMxC,UADN,EADF;QAGI,GAAG,EAAEO,YAFP;QAGE,KAAK,EAAE;YACL6B,aAAa,EAAErB,2BAA2B,GACtCE,sBAAsB,GACpB,MADoB,GAEpB,MAHoC,GAItC0B,SALC;YAML,GAAGnD,KAAK,CAAC2C,KAAT;SATJ;QAWE,cAAc,EAAE7D,4CAAoB,CAACkB,KAAK,CAACoD,cAAP,EAAuBjB,YAAY,CAACiB,cAApC,CAXtC;QAYE,aAAa,EAAEtE,4CAAoB,CAACkB,KAAK,CAACqD,aAAP,EAAsBlB,YAAY,CAACkB,aAAnC,CAZrC;QAaE,oBAAoB,EAAEvE,4CAAoB,CACxCkB,KAAK,CAACsD,oBADkC,EAExC5B,kBAAkB,CAAC4B,oBAFqB,CAA1C;KAbF,CAAA,CADF,CACE;CA7FmB,CAAzB,AAgHG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,4CAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMC,iCAAW,GAAG,wBAApB,AAAA;AAKA,MAAM7E,yCAAsB,GAAA,aAAGG,CAAAA,uBAAA,CAG7B,CAACmB,KAAD,EAAQC,YAAR,GAAyB;IACzB,MAAMQ,OAAO,GAAG5B,uBAAA,CAAiBY,6CAAjB,CAAhB,AAAA;IACA,MAAM+D,GAAG,GAAG3E,mBAAA,CAA4C,IAA5C,CAAZ,AAAA;IACA,MAAMkC,YAAY,GAAG9B,8CAAe,CAACgB,YAAD,EAAeuD,GAAf,CAApC,AAAA;IAEA3E,sBAAA,CAAgB,IAAM;QACpB,MAAM8B,IAAI,GAAG6C,GAAG,CAACE,OAAjB,AAAA;QACA,IAAI/C,IAAJ,EAAU;YACRF,OAAO,CAACX,QAAR,CAAiB+C,GAAjB,CAAqBlC,IAArB,CAAAF,CAAAA;YACA,OAAO,IAAM;gBACXA,OAAO,CAACX,QAAR,CAAiBiD,MAAjB,CAAwBpC,IAAxB,CAAAF,CAAAA;aADF,CAEC;SACF;KAPH,EAQG;QAACA,OAAO,CAACX,QAAT;KARH,CAQC,CAAA;IAED,OAAA,aAAO,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EAAA,2DAAA,CAAA,EAAA,EAAmBE,KAAnB,EAAP;QAAiC,GAAG,EAAEe,YAAL;KAA1B,CAAA,CAAP,CAAO;CAlBsB,CAA/B,AAmBC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,iCAAA;CAAA,CAAA,CAAA;AAEA,oGAAA,CAKA;;;;GAIA,CACA,SAASY,2CAAT,CAA+BvB,oBAA/B,EAAgG;IAC9F,MAAMuD,wBAAwB,GAAGzE,gDAAc,CAACkB,oBAAD,CAA/C,AAAA;IACA,MAAMwD,2BAA2B,GAAG/E,mBAAA,CAAa,KAAb,CAApC,AAAA;IACA,MAAMgF,cAAc,GAAGhF,mBAAA,CAAa,IAAM,EAAnB,CAAvB,AAAA;IAEAA,sBAAA,CAAgB,IAAM;QACpB,MAAMiF,iBAAiB,GAAIlC,CAAAA,KAAD,GAAyB;YACjD,IAAIA,KAAK,CAACC,MAAN,IAAgB,CAAC+B,2BAA2B,CAACF,OAAjD,EAA0D;gBACxD,MAAMK,WAAW,GAAG;oBAAEC,aAAa,EAAEpC,KAAfoC;iBAAtB,AAAoB;gBAEpB,SAASC,wCAAT,GAAoD;oBAClDC,kDAA4B,CAC1B5E,0CAD0B,EAE1BqE,wBAF0B,EAG1BI,WAH0B,EAI1B;wBAAEI,QAAQ,EAAE,IAAVA;qBAJwB,CAA5B,CAIE;iBAEH;gBAED;;;;;;;;;;;WAWR,CACQ,IAAIvC,KAAK,CAACwC,WAAN,KAAsB,OAA1B,EAAmC;oBACjC3B,QAAQ,CAACS,mBAAT,CAA6B,OAA7B,EAAsCW,cAAc,CAACH,OAArD,CAAAjB,CAAAA;oBACAoB,cAAc,CAACH,OAAf,GAAyBO,wCAAzB,CAAAJ;oBACApB,QAAQ,CAACQ,gBAAT,CAA0B,OAA1B,EAAmCY,cAAc,CAACH,OAAlD,EAA2D;wBAAEW,IAAI,EAAE,IAANA;qBAA7D,CAA2D,CAAA;iBAH7D,MAKEJ,wCAAwC,EAAxCA,CAAAA;aAEH;YACDL,2BAA2B,CAACF,OAA5B,GAAsC,KAAtC,CAAAE;SAjCF,AAkCC;QACD;;;;;;;;;;;;OAYJ,CACI,MAAMU,OAAO,GAAGC,MAAM,CAACC,UAAP,CAAkB,IAAM;YACtC/B,QAAQ,CAACQ,gBAAT,CAA0B,aAA1B,EAAyCa,iBAAzC,CAAArB,CAAAA;SADc,EAEb,CAFa,CAAhB,AAEC;QACD,OAAO,IAAM;YACX8B,MAAM,CAACE,YAAP,CAAoBH,OAApB,CAAAC,CAAAA;YACA9B,QAAQ,CAACS,mBAAT,CAA6B,aAA7B,EAA4CY,iBAA5C,CAAArB,CAAAA;YACAA,QAAQ,CAACS,mBAAT,CAA6B,OAA7B,EAAsCW,cAAc,CAACH,OAArD,CAAAjB,CAAAA;SAHF,CAIC;KAxDH,EAyDG;QAACkB,wBAAD;KAzDH,CAyDC,CAAA;IAED,OAAO;QACL,4DAAA;QACAL,oBAAoB,EAAE,IAAOM,2BAA2B,CAACF,OAA5B,GAAsC,IAAnEJ;KAFF,CAAO;CAIR;AAED;;;GAGA,CACA,SAASlB,qCAAT,CAAyB/B,cAAzB,EAA8E;IAC5E,MAAMqE,kBAAkB,GAAGxF,gDAAc,CAACmB,cAAD,CAAzC,AAAA;IACA,MAAMsE,yBAAyB,GAAG9F,mBAAA,CAAa,KAAb,CAAlC,AAAA;IAEAA,sBAAA,CAAgB,IAAM;QACpB,MAAM+F,WAAW,GAAIhD,CAAAA,KAAD,GAAuB;YACzC,IAAIA,KAAK,CAACC,MAAN,IAAgB,CAAC8C,yBAAyB,CAACjB,OAA/C,EAAwD;gBACtD,MAAMK,WAAW,GAAG;oBAAEC,aAAa,EAAEpC,KAAfoC;iBAAtB,AAAoB;gBACpBE,kDAA4B,CAAC3E,mCAAD,EAAgBmF,kBAAhB,EAAoCX,WAApC,EAAiD;oBAC3EI,QAAQ,EAAE,KAAVA;iBAD0B,CAA5B,CAA6E;aAG9E;SANH,AAOC;QACD1B,QAAQ,CAACQ,gBAAT,CAA0B,SAA1B,EAAqC2B,WAArC,CAAAnC,CAAAA;QACA,OAAO,IAAMA,QAAQ,CAACS,mBAAT,CAA6B,SAA7B,EAAwC0B,WAAxC,CAAb;QAAA,CAAA;KAVF,EAWG;QAACF,kBAAD;KAXH,CAWC,CAAA;IAED,OAAO;QACLtB,cAAc,EAAE,IAAOuB,yBAAyB,CAACjB,OAA1B,GAAoC,IADtD;QAAA;QAELL,aAAa,EAAE,IAAOsB,yBAAyB,CAACjB,OAA1B,GAAoC,KAA1DL;KAFF,CAAO;CAIR;AAED,SAASP,oCAAT,GAA0B;IACxB,MAAMlB,KAAK,GAAG,IAAIiD,WAAJ,CAAgBxF,oCAAhB,CAAd,AAAA;IACAoD,QAAQ,CAACqC,aAAT,CAAuBlD,KAAvB,CAAAa,CAAAA;CACD;AAED,SAASyB,kDAAT,CACEa,IADF,EAEEC,OAFF,EAGEC,MAHF,EAIE,E,UAAEd,QAAAA,CAAAA,EAJJ,EAKE;IACA,MAAMtC,MAAM,GAAGoD,MAAM,CAACjB,aAAP,CAAqBnC,MAApC,AAAA;IACA,MAAMD,KAAK,GAAG,IAAIiD,WAAJ,CAAgBE,IAAhB,EAAsB;QAAEG,OAAO,EAAE,KAAX;QAAkBC,UAAU,EAAE,IAA9B;Q,QAAoCF,MAAAA;KAA1D,CAAd,AAAoC;IACpC,IAAID,OAAJ,EAAanD,MAAM,CAACoB,gBAAP,CAAwB8B,IAAxB,EAA8BC,OAA9B,EAAwD;QAAEX,IAAI,EAAE,IAANA;KAA1D,CAAwD,CAAA;IAErE,IAAIF,QAAJ,EACEnF,wDAA2B,CAAC6C,MAAD,EAASD,KAAT,CAA3B,CAAA5C;SAEA6C,MAAM,CAACiD,aAAP,CAAqBlD,KAArB,CAAAC,CAAAA;CAEH;AAED,MAAMlD,yCAAI,GAAGF,yCAAb,AAAA;AACA,MAAMG,yCAAM,GAAGF,yCAAf,AAAA;;ADrVA", "sources": ["packages/react/dismissable-layer/src/index.ts", "packages/react/dismissable-layer/src/DismissableLayer.tsx"], "sourcesContent": ["export {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n} from './DismissableLayer';\nexport type { DismissableLayerProps } from './DismissableLayer';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useEscapeKeydown } from '@radix-ui/react-use-escape-keydown';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayer\n * -----------------------------------------------------------------------------------------------*/\n\nconst DISMISSABLE_LAYER_NAME = 'DismissableLayer';\nconst CONTEXT_UPDATE = 'dismissableLayer.update';\nconst POINTER_DOWN_OUTSIDE = 'dismissableLayer.pointerDownOutside';\nconst FOCUS_OUTSIDE = 'dismissableLayer.focusOutside';\n\nlet originalBodyPointerEvents: string;\n\nconst DismissableLayerContext = React.createContext({\n  layers: new Set<DismissableLayerElement>(),\n  layersWithOutsidePointerEventsDisabled: new Set<DismissableLayerElement>(),\n  branches: new Set<DismissableLayerBranchElement>(),\n});\n\ntype DismissableLayerElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DismissableLayerProps extends PrimitiveDivProps {\n  /**\n   * When `true`, hover/focus/click interactions will be disabled on elements outside\n   * the `DismissableLayer`. Users will need to click twice on outside elements to\n   * interact with them: once to close the `DismissableLayer`, and again to trigger the element.\n   */\n  disableOutsidePointerEvents?: boolean;\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: (event: KeyboardEvent) => void;\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void;\n  /**\n   * Event handler called when the focus moves outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onFocusOutside?: (event: FocusOutsideEvent) => void;\n  /**\n   * Event handler called when an interaction happens outside the `DismissableLayer`.\n   * Specifically, when a `pointerdown` event happens outside or focus moves outside of it.\n   * Can be prevented.\n   */\n  onInteractOutside?: (event: PointerDownOutsideEvent | FocusOutsideEvent) => void;\n  /**\n   * Handler called when the `DismissableLayer` should be dismissed\n   */\n  onDismiss?: () => void;\n}\n\nconst DismissableLayer = React.forwardRef<DismissableLayerElement, DismissableLayerProps>(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState<DismissableLayerElement | null>(null);\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setNode(node));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1); // prettier-ignore\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled); // prettier-ignore\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    });\n\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    });\n\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    });\n\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = document.body.style.pointerEvents;\n          document.body.style.pointerEvents = 'none';\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (\n          disableOutsidePointerEvents &&\n          context.layersWithOutsidePointerEventsDisabled.size === 1\n        ) {\n          document.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, disableOutsidePointerEvents, context]);\n\n    /**\n     * We purposefully prevent combining this effect with the `disableOutsidePointerEvents` effect\n     * because a change to `disableOutsidePointerEvents` would remove this layer from the stack\n     * and add it to the end again so the layering order wouldn't be _creation order_.\n     * We only want them to be removed from context stacks when unmounted.\n     */\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n\n    return (\n      <Primitive.div\n        {...layerProps}\n        ref={composedRefs}\n        style={{\n          pointerEvents: isBodyPointerEventsDisabled\n            ? isPointerEventsEnabled\n              ? 'auto'\n              : 'none'\n            : undefined,\n          ...props.style,\n        }}\n        onFocusCapture={composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture)}\n        onBlurCapture={composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture)}\n        onPointerDownCapture={composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )}\n      />\n    );\n  }\n);\n\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayerBranch\n * -----------------------------------------------------------------------------------------------*/\n\nconst BRANCH_NAME = 'DismissableLayerBranch';\n\ntype DismissableLayerBranchElement = React.ElementRef<typeof Primitive.div>;\ninterface DismissableLayerBranchProps extends PrimitiveDivProps {}\n\nconst DismissableLayerBranch = React.forwardRef<\n  DismissableLayerBranchElement,\n  DismissableLayerBranchProps\n>((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef<DismissableLayerBranchElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n\n  return <Primitive.div {...props} ref={composedRefs} />;\n});\n\nDismissableLayerBranch.displayName = BRANCH_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype PointerDownOutsideEvent = CustomEvent<{ originalEvent: PointerEvent }>;\ntype FocusOutsideEvent = CustomEvent<{ originalEvent: FocusEvent }>;\n\n/**\n * Listens for `pointerdown` outside a react subtree. We use `pointerdown` rather than `pointerup`\n * to mimic layer dismissing behaviour present in OS.\n * Returns props to pass to the node we want to check for outside events.\n */\nfunction usePointerDownOutside(onPointerDownOutside?: (event: PointerDownOutsideEvent) => void) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside) as EventListener;\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {});\n\n  React.useEffect(() => {\n    const handlePointerDown = (event: PointerEvent) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n\n        function handleAndDispatchPointerDownOutsideEvent() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        }\n\n        /**\n         * On touch devices, we need to wait for a click event because browsers implement\n         * a ~350ms delay between the time the user stops touching the display and when the\n         * browser executres events. We need to ensure we don't reactivate pointer-events within\n         * this timeframe otherwise the browser may execute events that should have been prevented.\n         *\n         * Additionally, this also lets us deal automatically with cancellations when a click event\n         * isn't raised because the page was considered scrolled/drag-scrolled, long-pressed, etc.\n         *\n         * This is why we also continuously remove the previous listener, because we cannot be\n         * certain that it was raised, and therefore cleaned-up.\n         */\n        if (event.pointerType === 'touch') {\n          document.removeEventListener('click', handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent;\n          document.addEventListener('click', handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent();\n        }\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    /**\n     * if this hook executes in a component that mounts via a `pointerdown` event, the event\n     * would bubble up to the document and trigger a `pointerDownOutside` event. We avoid\n     * this by delaying the event listener registration on the document.\n     * This is not React specific, but rather how the DOM works, ie:\n     * ```\n     * button.addEventListener('pointerdown', () => {\n     *   console.log('I will log');\n     *   document.addEventListener('pointerdown', () => {\n     *     console.log('I will also log');\n     *   })\n     * });\n     */\n    const timerId = window.setTimeout(() => {\n      document.addEventListener('pointerdown', handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      document.removeEventListener('pointerdown', handlePointerDown);\n      document.removeEventListener('click', handleClickRef.current);\n    };\n  }, [handlePointerDownOutside]);\n\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => (isPointerInsideReactTreeRef.current = true),\n  };\n}\n\n/**\n * Listens for when focus happens outside a react subtree.\n * Returns props to pass to the root (node) of the subtree we want to check.\n */\nfunction useFocusOutside(onFocusOutside?: (event: FocusOutsideEvent) => void) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside) as EventListener;\n  const isFocusInsideReactTreeRef = React.useRef(false);\n\n  React.useEffect(() => {\n    const handleFocus = (event: FocusEvent) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false,\n        });\n      }\n    };\n    document.addEventListener('focusin', handleFocus);\n    return () => document.removeEventListener('focusin', handleFocus);\n  }, [handleFocusOutside]);\n\n  return {\n    onFocusCapture: () => (isFocusInsideReactTreeRef.current = true),\n    onBlurCapture: () => (isFocusInsideReactTreeRef.current = false),\n  };\n}\n\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\n\nfunction handleAndDispatchCustomEvent<E extends CustomEvent, OriginalEvent extends Event>(\n  name: string,\n  handler: ((event: E) => void) | undefined,\n  detail: { originalEvent: OriginalEvent } & (E extends CustomEvent<infer D> ? D : never),\n  { discrete }: { discrete: boolean }\n) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler as EventListener, { once: true });\n\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\n\nconst Root = DismissableLayer;\nconst Branch = DismissableLayerBranch;\n\nexport {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n};\nexport type { DismissableLayerProps };\n"], "names": ["Dismissa<PERSON><PERSON><PERSON><PERSON>", "DismissableLayerBranch", "Root", "Branch", "React", "composeEventHandlers", "Primitive", "dispatchDiscreteCustomEvent", "useComposedRefs", "useCallbackRef", "useEscapeKeydown", "DISMISSABLE_LAYER_NAME", "CONTEXT_UPDATE", "POINTER_DOWN_OUTSIDE", "FOCUS_OUTSIDE", "originalBodyPointerEvents", "DismissableLayerContext", "createContext", "layers", "Set", "layersWithOutsidePointerEventsDisabled", "branches", "forwardRef", "props", "forwardedRef", "disableOutsidePointerEvents", "onEscapeKeyDown", "onPointerDownOutside", "onFocusOutside", "onInteractOutside", "on<PERSON><PERSON><PERSON>", "layerProps", "context", "useContext", "node", "setNode", "useState", "force", "composedRefs", "Array", "from", "highestLayerWithOutsidePointerEventsDisabled", "slice", "highestLayerWithOutsidePointerEventsDisabledIndex", "indexOf", "index", "isBodyPointerEventsDisabled", "size", "isPointerEventsEnabled", "pointerDownOutside", "usePointerDownOutside", "event", "target", "isPointerDownOnBranch", "some", "branch", "contains", "defaultPrevented", "focusOutside", "useFocusOutside", "isFocusInBranch", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "preventDefault", "useEffect", "document", "body", "style", "pointerEvents", "add", "dispatchUpdate", "delete", "handleUpdate", "addEventListener", "removeEventListener", "undefined", "onFocusCapture", "onBlurCapture", "onPointerDownCapture", "BRANCH_NAME", "ref", "useRef", "current", "handlePointerDownOutside", "isPointerInsideReactTreeRef", "handleClickRef", "handlePointerDown", "eventDetail", "originalEvent", "handleAndDispatchPointerDownOutsideEvent", "handleAndDispatchCustomEvent", "discrete", "pointerType", "once", "timerId", "window", "setTimeout", "clearTimeout", "handleFocusOutside", "isFocusInsideReactTreeRef", "handleFocus", "CustomEvent", "dispatchEvent", "name", "handler", "detail", "bubbles", "cancelable"], "version": 3, "file": "index.js.map"}