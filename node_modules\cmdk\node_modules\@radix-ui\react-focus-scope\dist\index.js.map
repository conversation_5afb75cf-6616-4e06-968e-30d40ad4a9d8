{"mappings": ";;;;;;;;;;;;;;;A;;;;;ACOA,MAAMM,wCAAkB,GAAG,6BAA3B,AAAA;AACA,MAAMC,0CAAoB,GAAG,+BAA7B,AAAA;AACA,MAAMC,mCAAa,GAAG;IAAEC,OAAO,EAAE,KAAX;IAAkBC,UAAU,EAAE,IAAZA;CAAxC,AAAsB;AAItB;;oGAEA,CAEA,MAAMC,sCAAgB,GAAG,YAAzB,AAAA;AAgCA,MAAMX,yCAAU,GAAA,aAAGE,CAAAA,uBAAA,CAAqD,CAACW,KAAD,EAAQC,YAAR,GAAyB;IAC/F,MAAM,QACJC,IAAI,GAAG,KADH,YAEJC,OAAO,GAAG,KAFN,GAGJC,gBAAgB,EAAEC,oBAHd,CAAA,EAIJC,kBAAkB,EAAEC,sBAJhB,CAAA,EAKJ,GAAGC,UAAH,EALI,GAMFR,KANJ,AAAM;IAON,MAAM,CAACS,UAAD,EAAYC,YAAZ,CAAA,GAA4BrB,qBAAA,CAAmC,IAAnC,CAAlC,AAAA;IACA,MAAMe,gBAAgB,GAAGZ,gDAAc,CAACa,oBAAD,CAAvC,AAAA;IACA,MAAMC,kBAAkB,GAAGd,gDAAc,CAACe,sBAAD,CAAzC,AAAA;IACA,MAAMK,qBAAqB,GAAGvB,mBAAA,CAAiC,IAAjC,CAA9B,AAAA;IACA,MAAMyB,YAAY,GAAGxB,8CAAe,CAACW,YAAD,EAAgBc,CAAAA,IAAD,GAAUL,YAAY,CAACK,IAAD,CAArC;IAAA,CAApC,AAAA;IAEA,MAAMC,UAAU,GAAG3B,mBAAA,CAAa;QAC9B4B,MAAM,EAAE,KADsB;QAE9BC,KAAK,IAAG;YACN,IAAA,CAAKD,MAAL,GAAc,IAAd,CAAA;SAH4B;QAK9BE,MAAM,IAAG;YACP,IAAA,CAAKF,MAAL,GAAc,KAAd,CAAA;SACD;KAPgB,CAAA,CAQhBG,OARH,AAd+F,EAwB/F,sFAVgC;IAWhC/B,sBAAA,CAAgB,IAAM;QACpB,IAAIc,OAAJ,EAAa;YACX,SAASmB,aAAT,CAAuBC,KAAvB,EAA0C;gBACxC,IAAIP,UAAU,CAACC,MAAX,IAAqB,CAACR,UAA1B,EAAqC,OAArC;gBACA,MAAMe,MAAM,GAAGD,KAAK,CAACC,MAArB,AAAA;gBACA,IAAIf,UAAS,CAACgB,QAAV,CAAmBD,MAAnB,CAAJ,EACEZ,qBAAqB,CAACQ,OAAtB,GAAgCI,MAAhC,CAAAZ;qBAEAc,2BAAK,CAACd,qBAAqB,CAACQ,OAAvB,EAAgC;oBAAEO,MAAM,EAAE,IAARA;iBAAlC,CAAL,CAAqC;aAExC;YAED,SAASC,cAAT,CAAwBL,KAAxB,EAA2C;gBACzC,IAAIP,UAAU,CAACC,MAAX,IAAqB,CAACR,UAA1B,EAAqC,OAArC;gBACA,IAAI,CAACA,UAAS,CAACgB,QAAV,CAAmBF,KAAK,CAACM,aAAzB,CAAL,EACEH,2BAAK,CAACd,qBAAqB,CAACQ,OAAvB,EAAgC;oBAAEO,MAAM,EAAE,IAARA;iBAAlC,CAAL,CAAqC;aAExC;YAEDG,QAAQ,CAACC,gBAAT,CAA0B,SAA1B,EAAqCT,aAArC,CAAAQ,CAAAA;YACAA,QAAQ,CAACC,gBAAT,CAA0B,UAA1B,EAAsCH,cAAtC,CAAAE,CAAAA;YACA,OAAO,IAAM;gBACXA,QAAQ,CAACE,mBAAT,CAA6B,SAA7B,EAAwCV,aAAxC,CAAAQ,CAAAA;gBACAA,QAAQ,CAACE,mBAAT,CAA6B,UAA7B,EAAyCJ,cAAzC,CAAAE,CAAAA;aAFF,CAGC;SACF;KAzBH,EA0BG;QAAC3B,OAAD;QAAUM,UAAV;QAAqBO,UAAU,CAACC,MAAhC;KA1BH,CA0BC,CAAA;IAED5B,sBAAA,CAAgB,IAAM;QACpB,IAAIoB,UAAJ,EAAe;YACbwB,sCAAgB,CAACC,GAAjB,CAAqBlB,UAArB,CAAAiB,CAAAA;YACA,MAAME,wBAAwB,GAAGL,QAAQ,CAACM,aAA1C,AAAA;YACA,MAAMC,mBAAmB,GAAG5B,UAAS,CAACgB,QAAV,CAAmBU,wBAAnB,CAA5B,AAAA;YAEA,IAAI,CAACE,mBAAL,EAA0B;gBACxB,MAAMC,UAAU,GAAG,IAAIC,WAAJ,CAAgB9C,wCAAhB,EAAoCE,mCAApC,CAAnB,AAAA;gBACAc,UAAS,CAACsB,gBAAV,CAA2BtC,wCAA3B,EAA+CW,gBAA/C,CAAAK,CAAAA;gBACAA,UAAS,CAAC+B,aAAV,CAAwBF,UAAxB,CAAA7B,CAAAA;gBACA,IAAI,CAAC6B,UAAU,CAACG,gBAAhB,EAAkC;oBAChCC,gCAAU,CAACC,iCAAW,CAACC,2CAAqB,CAACnC,UAAD,CAAtB,CAAZ,EAAgD;wBAAEkB,MAAM,EAAE,IAARA;qBAAlD,CAAV,CAA0D;oBAC1D,IAAIG,QAAQ,CAACM,aAAT,KAA2BD,wBAA/B,EACET,2BAAK,CAACjB,UAAD,CAAL,CAAAiB;iBAEH;aACF;YAED,OAAO,IAAM;gBACXjB,UAAS,CAACuB,mBAAV,CAA8BvC,wCAA9B,EAAkDW,gBAAlD,CAAA,CADW,CAGX,8DAFAK;gBAGA,gEAAA;gBACA,sDAAA;gBACAoC,UAAU,CAAC,IAAM;oBACf,MAAMC,YAAY,GAAG,IAAIP,WAAJ,CAAgB7C,0CAAhB,EAAsCC,mCAAtC,CAArB,AAAA;oBACAc,UAAS,CAACsB,gBAAV,CAA2BrC,0CAA3B,EAAiDY,kBAAjD,CAAAG,CAAAA;oBACAA,UAAS,CAAC+B,aAAV,CAAwBM,YAAxB,CAAArC,CAAAA;oBACA,IAAI,CAACqC,YAAY,CAACL,gBAAlB,EACEf,2BAAK,CAACS,wBAAD,KAAA,IAAA,IAACA,wBAAD,KAAA,KAAA,CAAA,GAACA,wBAAD,GAA6BL,QAAQ,CAACiB,IAAtC,EAA4C;wBAAEpB,MAAM,EAAE,IAARA;qBAA9C,CAAL,CAAiD;oBALpC,CAOf,0DADC;oBAEDlB,UAAS,CAACuB,mBAAV,CAA8BtC,0CAA9B,EAAoDY,kBAApD,CAAAG,CAAAA;oBAEAwB,sCAAgB,CAACe,MAAjB,CAAwBhC,UAAxB,CAAAiB,CAAAA;iBAVQ,EAWP,CAXO,CAAV,CAWC;aAjBH,CAkBC;SACF;KArCH,EAsCG;QAACxB,UAAD;QAAYL,gBAAZ;QAA8BE,kBAA9B;QAAkDU,UAAlD;KAtCH,CAAA,CArD+F,CA6F/F,iEAFC;IAGD,MAAMiC,aAAa,GAAG5D,wBAAA,CACnBkC,CAAAA,KAAD,GAAgC;QAC9B,IAAI,CAACrB,IAAD,IAAS,CAACC,OAAd,EAAuB,OAAvB;QACA,IAAIa,UAAU,CAACC,MAAf,EAAuB,OAAvB;QAEA,MAAMkC,QAAQ,GAAG5B,KAAK,CAAC6B,GAAN,KAAc,KAAd,IAAuB,CAAC7B,KAAK,CAAC8B,MAA9B,IAAwC,CAAC9B,KAAK,CAAC+B,OAA/C,IAA0D,CAAC/B,KAAK,CAACgC,OAAlF,AAAA;QACA,MAAMC,cAAc,GAAG1B,QAAQ,CAACM,aAAhC,AAAA;QAEA,IAAIe,QAAQ,IAAIK,cAAhB,EAAgC;YAC9B,MAAM/C,SAAS,GAAGc,KAAK,CAACkC,aAAxB,AAAA;YACA,MAAM,CAACC,KAAD,EAAQC,IAAR,CAAA,GAAgBC,sCAAgB,CAACnD,SAAD,CAAtC,AAAA;YACA,MAAMoD,yBAAyB,GAAGH,KAAK,IAAIC,IAA3C,AAH8B,EAK9B,mDAFA;YAGA,IAAI,CAACE,yBAAL,EACE;gBAAA,IAAIL,cAAc,KAAK/C,SAAvB,EAAkCc,KAAK,CAACuC,cAAN,EAAlC,CAAA;aAAA,MACK;gBACL,IAAI,CAACvC,KAAK,CAACwC,QAAP,IAAmBP,cAAc,KAAKG,IAA1C,EAAgD;oBAC9CpC,KAAK,CAACuC,cAAN,EAAAvC,CAAAA;oBACA,IAAIrB,IAAJ,EAAUwB,2BAAK,CAACgC,KAAD,EAAQ;wBAAE/B,MAAM,EAAE,IAARA;qBAAV,CAAL,CAAa;iBAFzB,MAGO,IAAIJ,KAAK,CAACwC,QAAN,IAAkBP,cAAc,KAAKE,KAAzC,EAAgD;oBACrDnC,KAAK,CAACuC,cAAN,EAAAvC,CAAAA;oBACA,IAAIrB,IAAJ,EAAUwB,2BAAK,CAACiC,IAAD,EAAO;wBAAEhC,MAAM,EAAE,IAARA;qBAAT,CAAL,CAAY;iBACvB;aACF;SACF;KAzBiB,EA2BpB;QAACzB,IAAD;QAAOC,OAAP;QAAgBa,UAAU,CAACC,MAA3B;KA3BoB,CAAtB,AA0BG;IAIH,OAAA,aACE,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EADF,2DAAA,CAAA;QACiB,QAAQ,EAAE,EAAV;KAAf,EAAiCT,UAAjC,EAAA;QAA6C,GAAG,EAAEM,YAAlD;QAAgE,SAAS,EAAEmC,aAAX;KAAhE,CAAA,CADF,CACE;CA7He,CAAnB,AA+HC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,sCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA;;;GAGA,CACA,SAASP,gCAAT,CAAoBsB,UAApB,EAA+C,UAAErC,MAAM,GAAG,KAATA,GAAF,GAAqB,EAApE,EAAwE;IACtE,MAAMQ,wBAAwB,GAAGL,QAAQ,CAACM,aAA1C,AAAA;IACA,KAAK,MAAM6B,SAAX,IAAwBD,UAAxB,CAAoC;QAClCtC,2BAAK,CAACuC,SAAD,EAAY;Y,QAAEtC,MAAAA;SAAd,CAAL,CAAiB;QACjB,IAAIG,QAAQ,CAACM,aAAT,KAA2BD,wBAA/B,EAAyD,OAAzD;KACD;CACF;AAED;;GAEA,CACA,SAASyB,sCAAT,CAA0BnD,SAA1B,EAAkD;IAChD,MAAMuD,UAAU,GAAGpB,2CAAqB,CAACnC,SAAD,CAAxC,AAAA;IACA,MAAMiD,KAAK,GAAGQ,iCAAW,CAACF,UAAD,EAAavD,SAAb,CAAzB,AAAA;IACA,MAAMkD,IAAI,GAAGO,iCAAW,CAACF,UAAU,CAACG,OAAX,EAAD,EAAuB1D,SAAvB,CAAxB,AAAA;IACA,OAAO;QAACiD,KAAD;QAAQC,IAAR;KAAP,CAAA;CACD;AAED;;;;;;;;;GASA,CACA,SAASf,2CAAT,CAA+BnC,SAA/B,EAAuD;IACrD,MAAM2D,KAAoB,GAAG,EAA7B,AAAA;IACA,MAAMC,MAAM,GAAGvC,QAAQ,CAACwC,gBAAT,CAA0B7D,SAA1B,EAAqC8D,UAAU,CAACC,YAAhD,EAA8D;QAC3EC,UAAU,EAAG1D,CAAAA,IAAD,GAAe;YACzB,MAAM2D,aAAa,GAAG3D,IAAI,CAAC4D,OAAL,KAAiB,OAAjB,IAA4B5D,IAAI,CAAC6D,IAAL,KAAc,QAAhE,AAAA;YACA,IAAI7D,IAAI,CAAC8D,QAAL,IAAiB9D,IAAI,CAAC+D,MAAtB,IAAgCJ,aAApC,EAAmD,OAAOH,UAAU,CAACQ,WAAlB,CAF1B,CAGzB,2EADA;YAEA,yEAAA;YACA,mDAAA;YACA,OAAOhE,IAAI,CAACiE,QAAL,IAAiB,CAAjB,GAAqBT,UAAU,CAACU,aAAhC,GAAgDV,UAAU,CAACQ,WAAlE,CAAA;SACD;KARY,CAAf,AAA6E;IAU7E,MAAOV,MAAM,CAACa,QAAP,EAAP,CAA0Bd,KAAK,CAACe,IAAN,CAAWd,MAAM,CAACe,WAAlB,CAAA,CAZ2B,CAarD,gFADA;IAEA,uEAAA;IACA,OAAOhB,KAAP,CAAA;CACD;AAED;;;GAGA,CACA,SAASF,iCAAT,CAAqBmB,QAArB,EAA8C5E,SAA9C,EAAsE;IACpE,KAAK,MAAM6E,OAAX,IAAsBD,QAAtB,CAAgC;QAC9B,uEAAA;QACA,IAAI,CAACE,8BAAQ,CAACD,OAAD,EAAU;YAAEE,IAAI,EAAE/E,SAAN+E;SAAZ,CAAb,EAA6C,OAAOF,OAAP,CAAtB;KACxB;CACF;AAED,SAASC,8BAAT,CAAkBxE,IAAlB,EAAqC,E,MAAEyE,IAAAA,CAAAA,EAAvC,EAAuE;IACrE,IAAIC,gBAAgB,CAAC1E,IAAD,CAAhB,CAAuB2E,UAAvB,KAAsC,QAA1C,EAAoD,OAAO,IAAP,CAApD;IACA,MAAO3E,IAAP,CAAa;QACX,mCAAA;QACA,IAAIyE,IAAI,KAAKG,SAAT,IAAsB5E,IAAI,KAAKyE,IAAnC,EAAyC,OAAO,KAAP,CAAzC;QACA,IAAIC,gBAAgB,CAAC1E,IAAD,CAAhB,CAAuB6E,OAAvB,KAAmC,MAAvC,EAA+C,OAAO,IAAP,CAA/C;QACA7E,IAAI,GAAGA,IAAI,CAAC8E,aAAZ,CAAA9E;KACD;IACD,OAAO,KAAP,CAAA;CACD;AAED,SAAS+E,uCAAT,CAA2BR,OAA3B,EAA8F;IAC5F,OAAOA,OAAO,YAAYS,gBAAnB,IAAuC,QAAA,IAAYT,OAA1D,CAAA;CACD;AAED,SAAS5D,2BAAT,CAAe4D,OAAf,EAAiD,UAAE3D,MAAM,GAAG,KAATA,GAAF,GAAqB,EAAtE,EAA0E;IACxE,0CAAA;IACA,IAAI2D,OAAO,IAAIA,OAAO,CAAC5D,KAAvB,EAA8B;QAC5B,MAAMS,wBAAwB,GAAGL,QAAQ,CAACM,aAA1C,AAD4B,EAE5B,iFADA;QAEAkD,OAAO,CAAC5D,KAAR,CAAc;YAAEsE,aAAa,EAAE,IAAfA;SAAhB,CAAA,CAH4B,CAI5B,uFADc;QAEd,IAAIV,OAAO,KAAKnD,wBAAZ,IAAwC2D,uCAAiB,CAACR,OAAD,CAAzD,IAAsE3D,MAA1E,EACE2D,OAAO,CAAC3D,MAAR,EADF,CAAA;KAED;CACF;AAED;;oGAEA,CAGA,MAAMM,sCAAgB,GAAGgE,4CAAsB,EAA/C,AAAA;AAEA,SAASA,4CAAT,GAAkC;IAChC,8DAAA,CACA,IAAIC,KAAsB,GAAG,EAA7B,AAAA;IAEA,OAAO;QACLhE,GAAG,EAAClB,UAAD,EAA4B;YAC7B,mEAAA;YACA,MAAMmF,gBAAgB,GAAGD,KAAK,CAAC,CAAD,CAA9B,AAAA;YACA,IAAIlF,UAAU,KAAKmF,gBAAnB,EACEA,gBAAgB,KAAA,IAAhB,IAAAA,gBAAgB,KAAA,KAAA,CAAhB,IAAAA,gBAAgB,CAAEjF,KAAlB,EAAAiF,CAAAA;YAJ2B,CAM7B,qFADC;YAEDD,KAAK,GAAGE,iCAAW,CAACF,KAAD,EAAQlF,UAAR,CAAnB,CAAAkF;YACAA,KAAK,CAACG,OAAN,CAAcrF,UAAd,CAAAkF,CAAAA;SATG;QAYLlD,MAAM,EAAChC,UAAD,EAA4B;YAAA,IAAA,OAAA,AAAA;YAChCkF,KAAK,GAAGE,iCAAW,CAACF,KAAD,EAAQlF,UAAR,CAAnB,CAAAkF;YACA,CAAA,OAAA,GAAAA,KAAK,CAAC,CAAD,CAAL,CAAA,KAAA,IAAA,IAAA,OAAA,KAAA,KAAA,CAAA,IAAA,OAAA,CAAU/E,MAAV,EAAA,CAAA;SACD;KAfH,CAAO;CAiBR;AAED,SAASiF,iCAAT,CAAwBE,KAAxB,EAAoCC,IAApC,EAA6C;IAC3C,MAAMC,YAAY,GAAG;WAAIF,KAAJ;KAArB,AAAA;IACA,MAAMG,KAAK,GAAGD,YAAY,CAACE,OAAb,CAAqBH,IAArB,CAAd,AAAA;IACA,IAAIE,KAAK,KAAK,EAAd,EACED,YAAY,CAACG,MAAb,CAAoBF,KAApB,EAA2B,CAA3B,CAAAD,CAAAA;IAEF,OAAOA,YAAP,CAAA;CACD;AAED,SAAS7D,iCAAT,CAAqBiE,KAArB,EAA2C;IACzC,OAAOA,KAAK,CAACC,MAAN,CAAcN,CAAAA,IAAD,GAAUA,IAAI,CAAC5B,OAAL,KAAiB,GAAxC;IAAA,CAAP,CAAA;CACD;AAED,MAAMvF,yCAAI,GAAGD,yCAAb,AAAA;;AD3TA", "sources": ["packages/react/focus-scope/src/index.ts", "packages/react/focus-scope/src/FocusScope.tsx"], "sourcesContent": ["export {\n  FocusScope,\n  //\n  Root,\n} from './FocusScope';\nexport type { FocusScopeProps } from './FocusScope';\n", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\nconst AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\ntype FocusableTarget = HTMLElement | { focus(): void };\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_SCOPE_NAME = 'FocusScope';\n\ntype FocusScopeElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FocusScopeProps extends PrimitiveDivProps {\n  /**\n   * When `true`, tabbing from last item will focus first tabbable\n   * and shift+tab from first item will focus last tababble.\n   * @defaultValue false\n   */\n  loop?: boolean;\n\n  /**\n   * When `true`, focus cannot escape the focus scope via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapped?: boolean;\n\n  /**\n   * Event handler called when auto-focusing on mount.\n   * Can be prevented.\n   */\n  onMountAutoFocus?: (event: Event) => void;\n\n  /**\n   * Event handler called when auto-focusing on unmount.\n   * Can be prevented.\n   */\n  onUnmountAutoFocus?: (event: Event) => void;\n}\n\nconst FocusScope = React.forwardRef<FocusScopeElement, FocusScopeProps>((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState<HTMLElement | null>(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef<HTMLElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    },\n  }).current;\n\n  // Takes care of trapping focus if focus is moved outside programmatically for example\n  React.useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const target = event.target as HTMLElement | null;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      function handleFocusOut(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        if (!container.contains(event.relatedTarget as HTMLElement | null)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement as HTMLElement | null;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n\n        // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          // we need to remove the listener after we `dispatchEvent`\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n\n  // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n\n      const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement as HTMLElement | null;\n\n      if (isTabKey && focusedElement) {\n        const container = event.currentTarget as HTMLElement;\n        const [first, last] = getTabbableEdges(container);\n        const hasTabbableElementsInside = first && last;\n\n        // we can only wrap focus if we have tabbable edges\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n\n  return (\n    <Primitive.div tabIndex={-1} {...scopeProps} ref={composedRefs} onKeyDown={handleKeyDown} />\n  );\n});\n\nFocusScope.displayName = FOCUS_SCOPE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Attempts focusing the first element in a list of candidates.\n * Stops when focus has actually moved.\n */\nfunction focusFirst(candidates: HTMLElement[], { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction getTabbableEdges(container: HTMLElement) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last] as const;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction findVisible(elements: HTMLElement[], container: HTMLElement) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\n\nfunction isHidden(node: HTMLElement, { upTo }: { upTo?: HTMLElement }) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement as HTMLElement;\n  }\n  return false;\n}\n\nfunction isSelectableInput(element: any): element is FocusableTarget & { select: () => void } {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\n\nfunction focus(element?: FocusableTarget | null, { select = false } = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({ preventScroll: true });\n    // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\n\ntype FocusScopeAPI = { paused: boolean; pause(): void; resume(): void };\nconst focusScopesStack = createFocusScopesStack();\n\nfunction createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */\n  let stack: FocusScopeAPI[] = [];\n\n  return {\n    add(focusScope: FocusScopeAPI) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n\n    remove(focusScope: FocusScopeAPI) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    },\n  };\n}\n\nfunction arrayRemove<T>(array: T[], item: T) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\n\nfunction removeLinks(items: HTMLElement[]) {\n  return items.filter((item) => item.tagName !== 'A');\n}\n\nconst Root = FocusScope;\n\nexport {\n  FocusScope,\n  //\n  Root,\n};\nexport type { FocusScopeProps };\n"], "names": ["FocusScope", "Root", "React", "useComposedRefs", "Primitive", "useCallbackRef", "AUTOFOCUS_ON_MOUNT", "AUTOFOCUS_ON_UNMOUNT", "EVENT_OPTIONS", "bubbles", "cancelable", "FOCUS_SCOPE_NAME", "forwardRef", "props", "forwardedRef", "loop", "trapped", "onMountAutoFocus", "onMountAutoFocusProp", "onUnmountAutoFocus", "onUnmountAutoFocusProp", "scopeProps", "container", "<PERSON><PERSON><PERSON><PERSON>", "useState", "lastFocusedElementRef", "useRef", "composedRefs", "node", "focusScope", "paused", "pause", "resume", "current", "useEffect", "handleFocusIn", "event", "target", "contains", "focus", "select", "handleFocusOut", "relatedTarget", "document", "addEventListener", "removeEventListener", "focusScopesStack", "add", "previouslyFocusedElement", "activeElement", "hasFocusedCandidate", "mountEvent", "CustomEvent", "dispatchEvent", "defaultPrevented", "focusFirst", "removeLinks", "getTabbableCandidates", "setTimeout", "unmountEvent", "body", "remove", "handleKeyDown", "useCallback", "isTabKey", "key", "altKey", "ctrl<PERSON>ey", "metaKey", "focusedElement", "currentTarget", "first", "last", "getTabbableEdges", "hasTabbableElementsInside", "preventDefault", "shift<PERSON>ey", "candidates", "candidate", "findVisible", "reverse", "nodes", "walker", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "acceptNode", "isHiddenInput", "tagName", "type", "disabled", "hidden", "FILTER_SKIP", "tabIndex", "FILTER_ACCEPT", "nextNode", "push", "currentNode", "elements", "element", "isHidden", "upTo", "getComputedStyle", "visibility", "undefined", "display", "parentElement", "isSelectableInput", "HTMLInputElement", "preventScroll", "createFocusScopesStack", "stack", "activeFocusScope", "arrayRemove", "unshift", "array", "item", "updatedArray", "index", "indexOf", "splice", "items", "filter"], "version": 3, "file": "index.js.map"}