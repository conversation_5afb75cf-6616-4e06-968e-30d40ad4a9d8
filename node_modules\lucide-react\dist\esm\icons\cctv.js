/**
 * @license lucide-react v0.312.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const Cctv = createLucideIcon("Cctv", [
  ["path", { d: "M7 9h.01", key: "19b3jx" }],
  ["path", { d: "M16.75 12H22l-3.5 7-3.09-4.32", key: "1h9vqe" }],
  [
    "path",
    {
      d: "M18 9.5l-4 8-10.39-5.2a2.92 2.92 0 0 1-1.3-3.91L3.69 5.6a2.92 2.92 0 0 1 3.92-1.3Z",
      key: "q5d122"
    }
  ],
  ["path", { d: "M2 19h3.76a2 2 0 0 0 1.8-1.1L9 15", key: "19bib8" }],
  ["path", { d: "M2 21v-4", key: "l40lih" }]
]);

export { Cctv as default };
//# sourceMappingURL=cctv.js.map
