{"mappings": ";;ACAA;AAIA;;;GAGA,CACA,SAASG,4BAAT,CAAmBC,GAAnB,EAAwCC,KAAxC,EAAkD;IAChD,IAAI,OAAOD,GAAP,KAAe,UAAnB,EACEA,GAAG,CAACC,KAAD,CAAH,CAAAD;SACK,IAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKE,SAA5B,EACJF,GAAD,CAAmCG,OAAnC,GAA6CF,KAA7C,CAACD;CAEJ;AAED;;;GAGA,CACA,SAASJ,yCAAT,CAAwB,GAAGQ,IAA3B,EAAmD;IACjD,OAAQC,CAAAA,IAAD,GAAaD,IAAI,CAACE,OAAL,CAAcN,CAAAA,GAAD,GAASD,4BAAM,CAACC,GAAD,EAAMK,IAAN,CAA5B;QAAA,CAApB;IAAA,CAAA;CACD;AAED;;;GAGA,CACA,SAASR,yCAAT,CAA4B,GAAGO,IAA/B,EAAuD;IACrD,uDAAA;IACA,OAAON,kBAAA,CAAkBF,yCAAW,IAAIQ,IAAJ,CAA7B,EAAwCA,IAAxC,CAAP,CAAA;CACD;;AD/BD", "sources": ["packages/react/compose-refs/src/index.ts", "packages/react/compose-refs/src/composeRefs.tsx"], "sourcesContent": ["export { composeRefs, useComposedRefs } from './composeRefs';\n", "import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    (ref as React.MutableRefObject<T>).current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]) {\n  return (node: T) => refs.forEach((ref) => setRef(ref, node));\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]) {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n"], "names": ["composeRefs", "useComposedRefs", "React", "setRef", "ref", "value", "undefined", "current", "refs", "node", "for<PERSON>ach", "useCallback"], "version": 3, "file": "index.module.js.map"}