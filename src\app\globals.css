@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom styles for the psychiatric assessment app */
.assessment-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
}

.form-section {
  @apply space-y-6 p-8 bg-white rounded-xl border border-slate-200 shadow-lg;
  transition: all 0.2s ease-in-out;
}

.form-section:hover {
  @apply shadow-xl border-slate-300;
  transform: translateY(-1px);
}

.progress-indicator {
  @apply flex items-center space-x-2 text-sm text-muted-foreground;
}

.autosave-indicator {
  @apply flex items-center space-x-2 text-sm text-emerald-600 bg-emerald-50 px-3 py-1.5 rounded-full border border-emerald-200;
  font-weight: 500;
}

.error-message {
  @apply text-sm text-red-600 mt-2 p-2 bg-red-50 border border-red-200 rounded-md;
}

.success-message {
  @apply text-sm text-emerald-600 mt-2 p-2 bg-emerald-50 border border-emerald-200 rounded-md;
}

/* Enhanced assessment form styling */
.assessment-header {
  @apply bg-white rounded-xl shadow-sm border border-slate-200 p-6 mb-8;
}

.assessment-progress-card {
  @apply bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200;
}

.assessment-tabs {
  @apply bg-white rounded-xl shadow-lg border border-slate-200 overflow-hidden;
}

.assessment-tab-trigger {
  @apply transition-all duration-200 hover:bg-slate-50;
}

.assessment-tab-trigger[data-state="active"] {
  @apply bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-md;
}

.assessment-tab-content {
  @apply p-8 bg-white;
}

/* Form input enhancements */
.form-input-enhanced {
  @apply transition-all duration-200 border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200;
}

.form-label-enhanced {
  @apply text-slate-700 font-medium text-sm mb-2;
}

.form-card-enhanced {
  @apply bg-white rounded-lg border border-slate-200 shadow-sm hover:shadow-md transition-all duration-200;
}

.symptom-category-card {
  @apply bg-gradient-to-br from-slate-50 to-slate-100 border-slate-200 hover:from-blue-50 hover:to-indigo-50 hover:border-blue-300 transition-all duration-300;
}

.symptom-checkbox {
  @apply w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 focus:ring-2;
}

.symptom-label {
  @apply text-slate-700 text-sm font-medium cursor-pointer hover:text-blue-700 transition-colors duration-200;
}
