/**
 * @license lucide-react v0.312.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const Mic2 = createLucideIcon("Mic2", [
  ["path", { d: "m12 8-9.04 9.06a2.82 2.82 0 1 0 3.98 3.98L16 12", key: "zoua8r" }],
  ["circle", { cx: "17", cy: "7", r: "5", key: "1fomce" }]
]);

export { Mic2 as default };
//# sourceMappingURL=mic-2.js.map
