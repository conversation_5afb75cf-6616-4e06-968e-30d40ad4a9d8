{"mappings": ";;;;;;AA0BA,OAAA,wFAAgF,CAAC;AAgBjF;IACE,QAAQ,CAAC,EAAE,MAAM,SAAS,CAAC;IAC3B,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,YAAY,CAAC,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,CAAC;IACnC,KAAK,CAAC,EAAE,OAAO,CAAC;CACjB;AAED,OAAA,MAAM,QAAQ,MAAM,EAAE,CAAC,WAAW,CAiCjC,CAAC;AAWF,4BAA4B,MAAM,wBAAwB,CAAC,OAAO,UAAU,MAAM,CAAC,CAAC;AACpF,mCAA6B,SAAQ,oBAAoB;CAAG;AAE5D,OAAA,MAAM,2GAkBL,CAAC;AAeF,mBAAmB,MAAM,wBAAwB,CAAC,eAAsB,CAAC,CAAC;AAC1E,kCAA4B,SAAQ,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC;IAC9D,QAAQ,CAAC,EAAE,MAAM,SAAS,CAAC;IAC3B;;;OAGG;IACH,UAAU,CAAC,EAAE,IAAI,CAAC;CACnB;AAED,OAAA,MAAM,cAAc,MAAM,EAAE,CAAC,iBAAiB,CAc7C,CAAC;AAWF,mCAA6B,SAAQ,sBAAsB;IACzD;;;OAGG;IACH,UAAU,CAAC,EAAE,IAAI,CAAC;CACnB;AAED,OAAA,MAAM,wGAWL,CAAC;AAKF,yBAAyB,MAAM,wBAAwB,CAAC,OAAO,UAAU,GAAG,CAAC,CAAC;AAC9E,gCAAiC,SAAQ,iBAAiB;CAAG;AA6B7D,mCAA6B,SAAQ,sBAAsB;IACzD;;;OAGG;IACH,UAAU,CAAC,EAAE,IAAI,CAAC;CACnB;AAED,OAAA,MAAM,wGAeL,CAAC;AAOF,gCACE,SAAQ,IAAI,CAAC,sBAAsB,EAAE,WAAW,GAAG,6BAA6B,CAAC;CAAG;AA4FtF,6BAA6B,MAAM,wBAAwB,CAAC,uBAAuB,CAAC,CAAC;AACrF,uBAAuB,MAAM,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;AACzE,gCAAiC,SAAQ,IAAI,CAAC,qBAAqB,EAAE,WAAW,CAAC;IAC/E;;;;OAIG;IACH,SAAS,CAAC,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;IAEvC;;;OAGG;IACH,eAAe,CAAC,EAAE,eAAe,CAAC,kBAAkB,CAAC,CAAC;IAEtD;;;OAGG;IACH,gBAAgB,CAAC,EAAE,eAAe,CAAC,oBAAoB,CAAC,CAAC;CAC1D;AAmDD,8BAA8B,MAAM,wBAAwB,CAAC,OAAO,UAAU,EAAE,CAAC,CAAC;AAClF,iCAA2B,SAAQ,sBAAsB;CAAG;AAE5D,OAAA,MAAM,wGAML,CAAC;AAWF,+BAA+B,MAAM,wBAAwB,CAAC,OAAO,UAAU,CAAC,CAAC,CAAC;AAClF,uCAAiC,SAAQ,uBAAuB;CAAG;AAEnE,OAAA,MAAM,sHAML,CAAC;AAWF,iCAA2B,SAAQ,oBAAoB;CAAG;AAE1D,OAAA,MAAM,uGAaL,CAAC;AAYF,OAAA;;;;;;;;;CAIE,CAAC;AA8CH,OAAA,MAAM,2BAAa,CAAC;AACpB,OAAA,MAAM,qGAAuB,CAAC;AAC9B,OAAA,MAAM,mCAAqB,CAAC;AAC5B,OAAA,MAAM,kGAAuB,CAAC;AAC9B,OAAA,MAAM,kGAAuB,CAAC;AAC9B,OAAA,MAAM,kGAAmB,CAAC;AAC1B,OAAA,MAAM,gHAA+B,CAAC;AACtC,OAAA,MAAM,iGAAmB,CAAC", "sources": ["packages/react/dialog/src/packages/react/dialog/src/Dialog.tsx", "packages/react/dialog/src/packages/react/dialog/src/index.ts", "packages/react/dialog/src/index.ts"], "sourcesContent": [null, null, "export {\n  createDialogScope,\n  //\n  Dialog,\n  DialogTrigger,\n  DialogPortal,\n  DialogOverlay,\n  DialogContent,\n  DialogTitle,\n  DialogDescription,\n  DialogClose,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Title,\n  Description,\n  Close,\n  //\n  WarningProvider,\n} from './Dialog';\nexport type {\n  DialogProps,\n  DialogTriggerProps,\n  DialogPortalProps,\n  DialogOverlayProps,\n  DialogContentProps,\n  DialogTitleProps,\n  DialogDescriptionProps,\n  DialogCloseProps,\n} from './Dialog';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}